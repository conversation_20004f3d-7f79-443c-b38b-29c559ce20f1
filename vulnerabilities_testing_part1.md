Voici une **documentation complète** de la vulnérabilité **n°1 — Credentials hardcodés**, incluant tous les tests que tu viens d’effectuer ✅

---

## 🔴 Vulnérabilité 1 — Credentials hardcodés

### 📌 Description :

Des identifiants sensibles (API Key EsimGo, clé Stripe live) sont présents **en clair dans le code source**, rendant l’application vulnérable à des usages non autorisés en cas d’accès au dépôt.

---

### 🧪 Tests réalisés :

#### ✅ Recherche dans le code

```powershell
Get-ChildItem -Recurse -File -Path .\simeo-api\ | Select-String "jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X"
```

**Résultat :**
Clé EsimGo retrouvée dans :

* `SyncBundles.php`
* `SyncCountries.php`
* `ListController.php`
* `Helpers/getApiKey.php`

---

#### ✅ Vérification d’exposition HTTP

```powershell
Invoke-WebRequest "https://phpstack-1478157-5631999.cloudwaysapps.com/.env"
Invoke-WebRequest "https://phpstack-1478157-5631999.cloudwaysapps.com/app/Console/Commands/SyncBundles.php"
```

**Résultat :**

* `.env` → 403 Forbidden ✅
* `SyncBundles.php` → Connexion fermée ✅

> Aucune exposition directe détectée.

---

#### ✅ Test de l’API Key EsimGo

```powershell
Invoke-WebRequest -Uri "https://api.esim-go.com/v2/bundles" `
  -Headers @{ "X-API-Key" = "jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X" } `
  -Method Get
```

**Réponse :**

```json
{"message":"access denied"}
```

> La clé est invalide, expirée ou révoquée ✅

---

#### ❌ Test de la clé Stripe (ACTIVE)

```powershell
Invoke-WebRequest -Uri "https://api.stripe.com/v1/customers" `
  -Headers @{ Authorization = "Bearer sk_live_..." } `
  -Method Get
```

**Réponse :**

```http
StatusCode        : 200 OK
Content           : {
  "object": "list",
  "data": [
    {
      "id": "cus_Sfts9URQEjZeRC",
      ...
```

> ⚠️ **Clé live active**, accès complet à la liste des clients Stripe → **faille critique**

---

### 🛠️ Recommandations de correction :

1. **Créer un fichier `.env` et y déplacer toutes les clés sensibles** :

   ```env
   ESIMGO_API_KEY=...
   STRIPE_SECRET_KEY=...
   ```

2. **Remplacer les appels hardcodés par `env()`** :

   ```php
   return env('ESIMGO_API_KEY');
   \Stripe\Stripe::setApiKey(env('STRIPE_SECRET_KEY'));
   ```

3. **Ajouter `.env` au `.gitignore`** :

   ```gitignore
   .env
   .env.local
   .env.production
   ```

4. **Révoquer immédiatement la clé Stripe compromise** depuis le dashboard.

5. **Purger l’historique Git si la clé a été commitée** :

   ```bash
   git log -S "sk_live_" -- simeo-api/
   ```

---

Souhaites-tu que je génère un rapport `.md` ou `.pdf` à partir de cette section pour ton livrable ?
Sinon, on peut passer à la vulnérabilité **n°2 : injection SQL (WordPress)**.
