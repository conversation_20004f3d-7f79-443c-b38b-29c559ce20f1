<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use DB;

class SyncBundles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:syncBundles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync bundles from apis';


    public $bundlesArray;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $this->bundlesArray = array();

        $this->syncEsimGo();

        //$this->syncDataPlans();

        $this->SyncDatabase();
    }

    public function SyncDatabase(){
        #region SYNC DATABASE

        // Get Options
        $outdatedBundlesTable = DB::select("SELECT meta_value FROM options WHERE meta_key='outdated_bundles_table'")[0]->meta_value;
        $updatedBundlesTable = DB::select("SELECT meta_value FROM options WHERE meta_key='updated_bundles_table'")[0]->meta_value;

        // Execute SYNC
        $truncate = DB::select("TRUNCATE TABLE " . $outdatedBundlesTable);
        foreach($this->bundlesArray as $bundle){

            // Conversion USA -> CAD
            $req_url = 'https://open.er-api.com/v6/latest/USD';
            $response_json = file_get_contents($req_url);

            // Continuing if we got a result
            if(false !== $response_json) {
                
                // Try/catch for json_decode operation
                try {
                    // Decoding
                    $response_object = json_decode($response_json);
                }
                catch(Exception $e) {
                    // Handle JSON parse error...
                }
                
                // Checking for errors
                if(isset($response_object->result) && 'error' == $response_object->result) {
                    // Handle API error... 
                }
                else {
                    // Yay! Everything is ok!
                    $conversionCAD = $response_object->rates->CAD;
                    $conversionEUR = $response_object->rates->EUR;
                    $bundle['priceEUR'] = floatval($bundle['price']) * $conversionEUR;
                    $bundle['priceUS'] = floatval($bundle['price']);
                    $bundle['price'] = floatval($bundle['price']) * $conversionCAD;
                    $bundle['currency'] = "CAD";
                }
            
            }
            
            $originalPrice = number_format($bundle['price'], 2, '.', '');
            // Price CAD
            
            // Normal Profit
            $caraibes = ["AG","AI","AN","BB","BM","BQ","BS","CW","DM","DO","GD","GF","GY","HT","JM","KN","KY","LC","MQ","MS","SR","SV","TC","TT","VC","VG"];
            if(in_array($bundle['main_country'], $caraibes)){
                $marge_de_profit = 0.20;
                $plus_de_profit = 0.30;
            }else if($bundle['main_country'] == "US"){
                $marge_de_profit = 0.30;
                $plus_de_profit = 0.30;
            }else{
                $marge_de_profit = 0.30;
                $plus_de_profit = 0.30;

                if($bundle['dataAmount']/1000 == 1){
                    $marge_de_profit += 0.20;
                    $plus_de_profit += 0.20;
                }

                if($bundle['dataAmount']/1000 == 2){
                    $marge_de_profit += 0.15;
                    $plus_de_profit += 0.15;
                }

                if($bundle['dataAmount']/1000 == 3){
                    $marge_de_profit += 0.10;
                    $plus_de_profit += 0.10;
                }

                if($bundle['dataAmount']/1000 == 5){
                    $marge_de_profit -= 0.00;
                    $plus_de_profit -= 0.00;
                }

                if($bundle['dataAmount']/1000 == 10){
                    $marge_de_profit -= 0.05;
                    $plus_de_profit -= 0.05;
                }

                if($bundle['dataAmount']/1000 == 20){
                    $marge_de_profit -= 0.15;
                    $plus_de_profit -= 0.15;
                }

                if($bundle['dataAmount']/1000 == 50){
                    $marge_de_profit -= 0.20;
                    $plus_de_profit -= 0.20;
                }
            }

            if($bundle['main_country_region'] == "Europe"){
                $marge_de_profit = 1.00;
                $plus_de_profit = 0.30;

                if($bundle['dataAmount']/1000 == 1){
                    $marge_de_profit += 0.50;
                    $plus_de_profit += 0.50;
                }

                if($bundle['dataAmount']/1000 == 2){
                    $marge_de_profit += 0.30;
                    $plus_de_profit += 0.30;
                }

                if($bundle['dataAmount']/1000 == 3){
                    $marge_de_profit += 0.30;
                    $plus_de_profit += 0.30;
                }

                if($bundle['dataAmount']/1000 == 5){
                    $marge_de_profit += 0.10;
                    $plus_de_profit += 0.10;
                }

                if($bundle['dataAmount']/1000 == 10){
                    $marge_de_profit += 0.00;
                    $plus_de_profit += 0.00;
                }

                if($bundle['dataAmount']/1000 == 20){
                    $marge_de_profit -= 0.25;
                    $plus_de_profit -= 0.25;
                }

                if($bundle['dataAmount']/1000 == 50){
                    $marge_de_profit -= 0.50;
                    $plus_de_profit -= 0.50;
                }
                
            }

            

            // GESTION DES RABAIS - PROFIT
            /*
            $black_friday_caraibes = ["AG","BS", "BB", "CO", "DM", "GD", "GT", "HN", "JM", "NI", "PA", "DO", "KN", "LC", "VC", "TT" ];
            $black_friday_south_america = ["MX", "CR"];
            if(in_array($bundle['main_country'], $black_friday_caraibes)){
                $marge_de_profit = 0.05;
            }
            if(in_array($bundle['main_country'], $black_friday_south_america)){
                $marge_de_profit = 0.15;
            }*/
            
            
            $stripe_cover = (floatval($bundle['price']) * 0.029) + 0.30;
            $simeo_cover = (floatval($bundle['price']) * $marge_de_profit) + $plus_de_profit;
            $bundle['price'] = floatval($bundle['price']) + $stripe_cover + $simeo_cover;
            $bundle['price'] = number_format((float)$bundle['price'], 2, '.', '');
            $bundle['priceCAD'] = $bundle['price'];

            // Mettre décimal à 95 à $bundle['price']
            $partieEntiere = floor($bundle['price']);
            $bundle['price'] = $partieEntiere + 0.95;
            
            // Mettre décimal à 95 à $bundle['priceCAD']
            $partieEntiere = floor($bundle['priceCAD']);
            $bundle['priceCAD'] = $partieEntiere + 0.95;

            // Price US
            $stripe_cover = (floatval($bundle['priceUS']) * 0.029) + 0.10;
            $simeo_cover = (floatval($bundle['priceUS']) * 0.20) + 0.20;
            $bundle['priceUS'] = floatval($bundle['priceUS']) + $stripe_cover + $simeo_cover;
            $bundle['priceUS'] = number_format((float)$bundle['priceUS'], 2, '.', '');

            // Mettre décimal à 95 à $bundle['priceUS']
            $partieEntiere = floor($bundle['priceUS']);
            $bundle['priceUS'] = $partieEntiere + 0.95;

            // Price EUR
            $stripe_cover = (floatval($bundle['priceEUR']) * 0.029) + 0.10;
            $simeo_cover = (floatval($bundle['priceEUR']) * 0.20) + 0.20;
            $bundle['priceEUR'] = floatval($bundle['priceEUR']) + $stripe_cover + $simeo_cover;
            $bundle['priceEUR'] = number_format((float)$bundle['priceEUR'], 2, '.', '');

            // Mettre décimal à 95 à $bundle['priceEUR']
            $partieEntiere = floor($bundle['priceEUR']);
            $bundle['priceEUR'] = $partieEntiere + 0.95;

            // isUnlimited
            $isUnlimited = 0;
            if($bundle['dataAmount'] == -1){
                $isUnlimited = 1;
            }

            // isRoaming
            $isRoaming = 0;
            if($bundle['countries'] != "[]"){
                $isRoaming = 1;
            }

            // GESTION RABAIS - ENREGISTREMENT
            $currentBundle = DB::select("SELECT * FROM " . $updatedBundlesTable . " WHERE name = '" . $bundle['name'] . "'");
            if(count($currentBundle) > 0){
                $currentBundlePrice = $currentBundle[0]->price;
                if($currentBundlePrice > $bundle['price']){
                    // Calculer le pourcentage de rabais
                    $discountPercentage = $bundle['price'] * 100 / $currentBundlePrice;
                    $discountRealPercentage = number_format((float)$discountPercentage, 2, '.', '');

                    // Afficher le rabais en % en INT
                    $displayDiscount = ceil($discountRealPercentage);

                    // ADD OPPORTUNITY OF DISCOUNT
                    $results = DB::insert("INSERT INTO discounts (is_active, bundleName, price, regular_price, percentage, real_percentage, created_at) 
                                           VALUES ('0',
                                                   '" . $bundle['name'] . "', 
                                                   '" . $bundle['price'] . "', 
                                                   '" . $currentBundlePrice . "', 
                                                   '" . (100 - $displayDiscount) . "', 
                                                   '" . (100 - $discountRealPercentage) . "', 
                                                   '" . date("Ymd") . "')");
                }
            }
            
            $results = DB::select("INSERT INTO " . $outdatedBundlesTable . " (name, description, main_country, countries, dataAmount, duration, speed, autostart, imageUrl, price, currency, distributor, priceUS, priceEUR, priceCAD, distributorPrice, isUnlimited, isRoaming, percentage) 
                                   VALUES(  '" . $bundle['name'] . "' ,
                                            '" . $bundle['description'] . "',
                                            '" . $bundle['main_country'] . "',
                                            '" . $bundle['countries'] . "',
                                            '" . $bundle['dataAmount'] . "',
                                            '" . $bundle['duration'] . "',
                                            '" . $bundle['speed'] . "',
                                            '" . $bundle['autostart'] . "',
                                            '" . $bundle['imageUrl'] . "',
                                            '" . $bundle['price'] . "',
                                            '" . $bundle['currency'] . "',
                                            '" . $bundle['distributor'] . "',
                                            '" . $bundle['priceUS'] . "',
                                            '" . $bundle['priceEUR'] . "',
                                            '" . $bundle['priceCAD'] . "',
                                            '" . floatval($originalPrice) + floatval($stripe_cover) . "',
                                            '" . $isUnlimited . "',
                                            '" . $isRoaming . "',
                                            '" . $marge_de_profit . "' )");
        }

        // Update Options
        DB::select("UPDATE options SET meta_value = '" . date("Y-m-d H:i:s") . "' WHERE meta_key = '" . $outdatedBundlesTable . "_last_sync'");
        DB::select("UPDATE options SET meta_value = '" . $updatedBundlesTable . "' WHERE meta_key = 'outdated_bundles_table'");
        DB::select("UPDATE options SET meta_value = '" . $outdatedBundlesTable . "' WHERE meta_key = 'updated_bundles_table'");

        #endregion
    }

    public function syncEsimGo(){
        #region cURL ESIM-GO
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/catalogue?perPage=100000&group=Standard%20Fixed');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_NOBODY, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        $results_obj_esimgo = json_decode($res_esimgo);
        #endregion

        #region ESIM-GO SYNC RESULT
        foreach($results_obj_esimgo->bundles as $bundle){
            
            $countriesArray = array();

            if(is_array($bundle->speed)){
                $speed = end($bundle->speed);
            }else{
                $speed = "";
            }

            foreach($bundle->roamingEnabled as $country){
                $countriesArray[] = $country->iso;
            }

            $this->bundlesArray[] = array(
                'name' => $bundle->name,
                'description' => $bundle->description,
                'main_country' => $bundle->countries[0]->iso,
                'main_country_region' => $bundle->countries[0]->region,
                'dataAmount' => $bundle->dataAmount,
                'duration' => $bundle->duration,
                'speed' => $speed,
                'autostart' => $bundle->autostart,
                'countries' => json_encode($countriesArray),
                'imageUrl' => $bundle->imageUrl,
                'price' => number_format((float)$bundle->price, 2, '.', ''),
                'currency' => "USD",
                'distributor' => '1'
            );
        
        }
        #endregion
    }

    public function syncDataPlans(){
        #region cURL DATAPLANS.IO
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://app.dataplans.io/api/v1/plans');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzaWQiOiIwODJmNzYzZi0yZDQ3LTQ3NTQtYTJmNy0wYjZiYWQ2NDU0ZjYiLCJpYXQiOjE2ODE1NzAxNjMsImV4cCI6MjU0NTQ4Mzc2M30.lHeCeqcH8nCphwGMfcuj0aQ6mSVBZ_eHxzwKNQ6Fls8',
            'accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_NOBODY, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $res_dataplans = curl_exec($ch);
        curl_close($ch);

        $results_obj_dataplans = json_decode($res_dataplans);
        #endregion

        #region DATAPLANS.IO SYNC RESULT
        foreach($results_obj_dataplans as $bundle){
            
            $countriesArray = array();

            
            $speed = "";
            

            foreach($bundle->countries as $country){
                $countriesArray[] = $country->countryCode;
            }

            $capacity = $bundle->capacity;
            if($capacity != -1){
                $capacity = $capacity * 1000;
            }

            if($bundle->active && $capacity != 0 && $bundle->capacityUnit == 'GB'){
                $this->bundlesArray[] = array(
                    'name' => $bundle->slug,
                    'description' => $bundle->name,
                    'main_country' => "",
                    'dataAmount' => $capacity,
                    'duration' => $bundle->period,
                    'speed' => $speed,
                    'autostart' => '1',
                    'countries' => json_encode($countriesArray),
                    'imageUrl' => "",
                    'price' => number_format((float)$bundle->retailPrice, 2, '.', ''),
                    'currency' => $bundle->priceCurrency,
                    'distributor' => '2'
                );
            }
        
        }
        #endregion
    }

    #region HELPERS
    public function array_orderby()
    {
        $args = func_get_args();
        $data = array_shift($args);
        foreach ($args as $n => $field) {
            if (is_string($field)) {
                $tmp = array();
                foreach ($data as $key => $row)
                    $tmp[$key] = $row[$field];
                $args[$n] = $tmp;
                }
        }
        $args[] = &$data;
        call_user_func_array('array_multisort', $args);
        return array_pop($args);
    }
    #endregion
}
