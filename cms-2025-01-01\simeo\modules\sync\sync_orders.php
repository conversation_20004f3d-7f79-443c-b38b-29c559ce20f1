<?php

$parse_uri = explode( 'wp-content', $_SERVER['SCRIPT_FILENAME'] );
require_once( $parse_uri[0] . 'wp-load.php' );

if ( ! is_admin() ) {
    require_once( ABSPATH . 'wp-admin/includes/post.php' );
}

//add debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
date_default_timezone_set('America/Montreal');
$DATABASE = 'kfgkkncdzz';
$USERNAME = 'kfgkkncdzz';
$PASSWORD = 'WJS5PE9rZx';
$conn = new mysqli('localhost', $USERNAME, $PASSWORD, $DATABASE);
$conn->set_charset("utf8");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}else{
    //echo "Connected successfully";

    // GET UPDATED BUNDLE TABLE
    $result = $conn->query("SELECT po.*, u.*, o.*, o.credit as creditOrder FROM orders o LEFT JOIN paymentOrders po ON o.id = po.id_order LEFT JOIN users u ON o.id_user = u.id ORDER BY o.date DESC LIMIT 100");

    // GET UPDATED BUNDLE TABLE
    $result2 = $conn->query("SELECT * FROM options WHERE meta_key = 'updated_bundles_table'");
    $result2 = $result2->fetch_assoc();
    $bundles_table = $result2['meta_value'];
    $array_orders = array();
    while($row = $result->fetch_assoc()) {

        
        $product_name = $row['product_name'];
        $product = explode(' - ', $product_name);
        $pays = $product[1];
        $data = $product[2];
        $validity = $product[3];
        $product_name_filtered = 'esim_' . $data . '_' . str_replace(' Jours', '', $validity) . 'D_' . get_iso_if_region($pays) . '_V2';

        $bundles_request = $conn->query("SELECT * FROM $bundles_table WHERE name = '$product_name_filtered'");
        if($bundles_request->num_rows > 0){
            $bundles = $bundles_request->fetch_assoc();
        }else{
            $bundles = null;
        }

        if(isset($row['payment_object'])){
            $payment_object = json_decode($row['payment_object']);
        }else{
            $payment_object = null;
        }

        $array_order = array(
            'info_client' => array(),
            'info_payment' => array(),
            'info_stats' => array()
        );

        $info_client = array(
            'id_user' => $row['id_user'],
            'stripe_id' => $row['id_customer'],
            'courriel_du_client' => $row['email']
        );

        $taxes = (floatval($row['total_paid']) * 0.05) + (floatval($row['total_paid']) * 0.09975);
        $taxesTPS = floatval($row['total_paid'] * 0.05);
        $taxesTVQ = floatval($row['total_paid'] * 0.09975);

        $product_name = isset($payment_object) ? $payment_object->metadata->bundleName : $product_name_filtered;

        $info_payment = array(
            'numero_de_transaction' => $row['id_order'],
            'produit_achete' => $product_name,
            'date_de_lachat' => date('Y-m-d H:i:s', $row['date']),
            'credits_utilise' => $row['creditOrder'],
            'sous-total' => $row['total_paid'],
            'taxesTPS' => number_format($taxesTPS, 2),
            'taxesTVQ' => number_format($taxesTVQ, 2),
            'total' => number_format(floatval($row['total_paid']) + floatval($taxes), 2),
            'payment_method' => $row['payment_method'],
            'payment_id' => isset($payment_object) ? $payment_object->id : '',
        );

        if(isset($bundles)){
            $cout_reel = floatval($bundles['distributorPrice']) + (floatval($bundles['distributorPrice']) * 0.038);
            $infos_stats = array(
                'cout_reel' => number_format(floatval($cout_reel), 2),
                'prix_vendu' => number_format(floatval($row['total_paid']), 2),
                'marge_de_profit' => number_format(floatval($row['total_paid']) - floatval($cout_reel), 2)
            );
        }else{
            $infos_stats = array(
                'cout_reel' => "Stats indisponible, Check ak turcotte",
                'prix_vendu' => "",
                'marge_de_profit' => ""
            );
        }

        $array_order = array(
            'info_client' => $info_client,
            'info_payment' => $info_payment,
            'info_stats' => $infos_stats
        );

        $array_orders[] = $array_order;
    }

    foreach($array_orders as $array_order){
        $post_title = 'Achat #' . $array_order['info_payment']['numero_de_transaction'] . ' - ' . $array_order['info_payment']['produit_achete'] . ' - ' . $array_order['info_client']['courriel_du_client'];

        if(post_exists($post_title)){
            $edit = 1;
            $post_id = post_exists($post_title);
        }else{
            $edit = 0;
            $post_id = wp_insert_post(array(
                'post_title' => $post_title,
                'post_content' => "",
                'post_status' => 'publish',
                'post_date' => $array_order['info_payment']['date_de_lachat'],
                'post_type' => 'orders',
                'post_name' => $array_order['info_payment']['numero_de_transaction']
            ));
        }

        update_post_meta($post_id, 'id_user', $array_order['info_client']['id_user']);
        update_post_meta($post_id, 'stripe_id', $array_order['info_client']['stripe_id']);
        update_post_meta($post_id, 'courriel_du_client', $array_order['info_client']['courriel_du_client']);

        update_post_meta($post_id, 'numero_de_transaction', $array_order['info_payment']['numero_de_transaction']);
        update_post_meta($post_id, 'produit_achete', $array_order['info_payment']['produit_achete']);
        update_post_meta($post_id, 'date_de_lachat', $array_order['info_payment']['date_de_lachat']);
        update_post_meta($post_id, 'credits_utilise', $array_order['info_payment']['credits_utilise']);
        update_post_meta($post_id, 'sous-total', $array_order['info_payment']['sous-total']);
        update_post_meta($post_id, 'taxes_tps', $array_order['info_payment']['taxesTPS']);
        update_post_meta($post_id, 'taxes_tvq', $array_order['info_payment']['taxesTVQ']);
        update_post_meta($post_id, 'total', $array_order['info_payment']['total']);
        update_post_meta($post_id, 'payment_method', $array_order['info_payment']['payment_method']);
        update_post_meta($post_id, 'payment_id', $array_order['info_payment']['payment_id']);

        update_post_meta($post_id, 'cout_reel', $array_order['info_stats']['cout_reel']);
        update_post_meta($post_id, 'prix_vendu', $array_order['info_stats']['prix_vendu']);
        update_post_meta($post_id, 'marge_de_profit', $array_order['info_stats']['marge_de_profit']);

        if($edit == 0){
            wp_set_object_terms($post_id, 'termine', 'status');
        }
    }

    die();
}

?>