<?php

$parse_uri = explode( 'wp-content', $_SERVER['SCRIPT_FILENAME'] );
require_once( $parse_uri[0] . 'wp-load.php' );

if ( ! is_admin() ) {
    require_once( ABSPATH . 'wp-admin/includes/post.php' );
}

//add debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$DATABASE = 'kfgkkncdzz';
$USERNAME = 'kfgkkncdzz';
$PASSWORD = 'WJS5PE9rZx';
$conn = new mysqli('localhost', $USERNAME, $PASSWORD, $DATABASE);

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}else{
    echo "Connected successfully";

    // GET UPDATED BUNDLE TABLE
    $result = $conn->query("SELECT * FROM options WHERE meta_key = 'updated_bundles_table'");
    $result = $result->fetch_assoc();
    $bundles_table = $result['meta_value'];

    // GET UPDATED COUNTRIES TABLE
    $result = $conn->query("SELECT * FROM options WHERE meta_key = 'updated_countries_table'");
    $result = $result->fetch_assoc();
    $countries_table = $result['meta_value'];

    // GET COUNTRIES TABLE
    $countries = $conn->query("SELECT * FROM " . $countries_table);


    while($country = $countries->fetch_assoc()){
        $name = $country['name'];
        $slug = $country['iso'];
        $region = $country['region'];

        // ADD_TAXONOMY
        if(term_exists($name, 'countries')){
            $term = get_term_by('name', $name, 'countries');
            $term_id = $term->term_id;
        }else{
            $term_id = wp_insert_term($name, 'countries', array(
                'slug' => $slug,
            ));
        }

        update_term_meta($term_id, 'region', $region);

    }

    // get bundles
    $bundles = $conn->query("SELECT * FROM " . $bundles_table);
    while($bundle = $bundles->fetch_assoc()){
        $roaming = $bundle['countries'];
        $main_country = $bundle['main_country'];
        $title = $bundle['description'];
        $slug = $bundle['name'];
        $price = $bundle['price'];
        $url_image = $bundle['imageUrl'];
        $data_amount = $bundle['dataAmount'];
        $duration = $bundle['duration'];
        $marge = 30;

        if(post_exists($title)){
            //echo "Bundle already exists";
            $post = get_page_by_title($title, OBJECT, 'bundle');
            $post_id = $post->ID;
            update_post_meta($post_id, 'roaming', $roaming);
            update_post_meta($post_id, 'quantite_en_mb', $data_amount);
            update_post_meta($post_id, 'validite_en_jours', $duration);
            update_post_meta($post_id, 'prix_cad', $price);
            update_post_meta($post_id, 'url_image', $url_image);
            update_post_meta($post_id, 'marge_profit', $marge);

            wp_set_object_terms($post_id, strtolower($main_country), 'countries');
        }else{
            //echo "Bundle does not exist";
            $post_id = wp_insert_post(array(
                'post_title' => $title,
                'post_name' => $slug,
                'post_status' => 'publish',
                'post_type' => 'bundle',
            ));

            update_post_meta($post_id, 'roaming', $roaming);
            update_post_meta($post_id, 'quantite_en_mb', $data_amount);
            update_post_meta($post_id, 'validite_en_jours', $duration);
            update_post_meta($post_id, 'prix_cad', $price);
            update_post_meta($post_id, 'url_image', $url_image);
            update_post_meta($post_id, 'marge_profit', $marge);

            wp_set_object_terms($post_id, strtolower($main_country), 'countries');
        }
        
    }

}


?>