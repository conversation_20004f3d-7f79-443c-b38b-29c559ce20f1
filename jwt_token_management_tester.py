#!/usr/bin/env python3
"""
Script de test de gestion des tokens JWT - API Simeo
Teste si les anciens tokens restent actifs après création de nouveaux tokens
"""

import requests
import json
import time
import urllib3

# Désactiver les warnings SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# CONFIGURATION
BASE_URL = "https://phpstack-1478157-5631999.cloudwaysapps.com/api"
LOGIN_EMAIL = "<EMAIL>"
LOGIN_PASSWORD = "NewPassword123!"

class JWTTokenTester:
    def __init__(self):
        self.session = requests.Session()
        self.tokens = []  # Liste pour stocker tous les tokens générés
        
    def login_and_get_token(self, attempt_number):
        """Se connecte et récupère un nouveau token"""
        print(f"\n[LOGIN {attempt_number}] Connexion pour obtenir un nouveau token...")
        
        login_data = {
            "email": LOGIN_EMAIL,
            "password": LOGIN_PASSWORD
        }
        
        try:
            response = self.session.post(
                f"{BASE_URL}/api/login", 
                json=login_data, 
                verify=False, 
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                token = data.get('access_token')
                if token:
                    print(f"[SUCCESS] Nouveau token obtenu: {token[:50]}...")
                    self.tokens.append({
                        'token': token,
                        'attempt': attempt_number,
                        'timestamp': time.time()
                    })
                    return token
                else:
                    print(f"[ERROR] Pas de token dans la réponse: {data}")
                    return None
            else:
                print(f"[ERROR] Échec de connexion: {response.status_code}")
                print(f"[ERROR] Réponse: {response.text}")
                return None
                
        except Exception as e:
            print(f"[ERROR] Erreur lors du login: {e}")
            return None
    
    def test_token_validity(self, token_info):
        """Teste si un token est encore valide"""
        token = token_info['token']
        attempt = token_info['attempt']
        
        print(f"\n[TEST] Test du token #{attempt} ({token[:20]}...)")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        try:
            # Test avec l'endpoint /api/me
            response = self.session.get(
                f"{BASE_URL}/api/me", 
                headers=headers, 
                verify=False, 
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                user_id = data.get('id', 'N/A')
                email = data.get('email', 'N/A')
                print(f"[VALID] ✅ Token #{attempt} ACTIF - User ID: {user_id}, Email: {email}")
                return True
            elif response.status_code == 401:
                print(f"[INVALID] ❌ Token #{attempt} EXPIRÉ/INVALIDE")
                return False
            else:
                print(f"[ERROR] Token #{attempt} - Status: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[ERROR] Erreur lors du test du token #{attempt}: {e}")
            return False
    
    def test_token_with_action(self, token_info):
        """Teste si un token peut effectuer des actions (changeActiveZone)"""
        token = token_info['token']
        attempt = token_info['attempt']
        
        print(f"\n[ACTION] Test d'action avec token #{attempt}")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        data = {
            "zone": f"TestZone{attempt}"
        }
        
        try:
            response = self.session.post(
                f"{BASE_URL}/api/changeActiveZone", 
                json=data,
                headers=headers, 
                verify=False, 
                timeout=10
            )
            
            if response.status_code == 200:
                print(f"[ACTION] ✅ Token #{attempt} peut effectuer des actions")
                return True
            elif response.status_code == 401:
                print(f"[ACTION] ❌ Token #{attempt} ne peut pas effectuer d'actions (401)")
                return False
            else:
                print(f"[ACTION] Token #{attempt} - Status: {response.status_code}, Response: {response.text[:100]}")
                return False
                
        except Exception as e:
            print(f"[ERROR] Erreur lors du test d'action du token #{attempt}: {e}")
            return False
    
    def run_token_management_test(self, num_logins=3):
        """Lance le test complet de gestion des tokens"""
        print("="*80)
        print("TEST DE GESTION DES TOKENS JWT")
        print("="*80)
        print(f"URL: {BASE_URL}")
        print(f"Email: {LOGIN_EMAIL}")
        print(f"Nombre de connexions à tester: {num_logins}")
        
        # Phase 1: Générer plusieurs tokens
        print("\n" + "="*50)
        print("PHASE 1: GÉNÉRATION DE TOKENS MULTIPLES")
        print("="*50)
        
        for i in range(1, num_logins + 1):
            token = self.login_and_get_token(i)
            if not token:
                print(f"[ERROR] Impossible d'obtenir le token #{i}")
                continue
            
            # Attendre un peu entre les connexions
            if i < num_logins:
                print(f"[INFO] Attente de 2 secondes avant la prochaine connexion...")
                time.sleep(2)
        
        print(f"\n[INFO] {len(self.tokens)} tokens générés au total")
        
        # Phase 2: Tester la validité de tous les tokens
        print("\n" + "="*50)
        print("PHASE 2: TEST DE VALIDITÉ DES TOKENS")
        print("="*50)
        
        valid_tokens = []
        for token_info in self.tokens:
            if self.test_token_validity(token_info):
                valid_tokens.append(token_info)
        
        print(f"\n[RÉSULTAT] {len(valid_tokens)}/{len(self.tokens)} tokens sont encore valides")
        
        # Phase 3: Tester les actions avec tous les tokens valides
        print("\n" + "="*50)
        print("PHASE 3: TEST D'ACTIONS AVEC LES TOKENS VALIDES")
        print("="*50)
        
        active_tokens = []
        for token_info in valid_tokens:
            if self.test_token_with_action(token_info):
                active_tokens.append(token_info)
        
        print(f"\n[RÉSULTAT] {len(active_tokens)}/{len(valid_tokens)} tokens peuvent effectuer des actions")
        
        # Phase 4: Résumé et analyse
        print("\n" + "="*50)
        print("PHASE 4: ANALYSE DES RÉSULTATS")
        print("="*50)
        
        print(f"\n📊 STATISTIQUES:")
        print(f"   • Tokens générés: {len(self.tokens)}")
        print(f"   • Tokens valides: {len(valid_tokens)}")
        print(f"   • Tokens actifs: {len(active_tokens)}")
        
        if len(active_tokens) > 1:
            print(f"\n🚨 VULNÉRABILITÉ DÉTECTÉE:")
            print(f"   • {len(active_tokens)} tokens sont simultanément actifs")
            print(f"   • Les anciens tokens ne sont PAS révoqués lors de nouvelles connexions")
            print(f"   • Un attaquant pourrait utiliser d'anciens tokens volés")
            
            print(f"\n📋 TOKENS ACTIFS:")
            for token_info in active_tokens:
                age = time.time() - token_info['timestamp']
                print(f"   • Token #{token_info['attempt']}: {token_info['token'][:30]}... (âge: {age:.1f}s)")
        
        elif len(active_tokens) == 1:
            print(f"\n✅ GESTION CORRECTE:")
            print(f"   • Seul le dernier token est actif")
            print(f"   • Les anciens tokens ont été correctement révoqués")
        
        else:
            print(f"\n❌ PROBLÈME:")
            print(f"   • Aucun token n'est actif")
            print(f"   • Possible problème de configuration")
        
        # Phase 5: Test de révocation manuelle (si possible)
        print("\n" + "="*50)
        print("PHASE 5: TEST DE RÉVOCATION")
        print("="*50)
        
        if len(active_tokens) > 0:
            print(f"\n[TEST] Tentative de logout avec le premier token actif...")
            first_token = active_tokens[0]
            
            headers = {
                "Authorization": f"Bearer {first_token['token']}",
                "Content-Type": "application/json"
            }
            
            try:
                response = self.session.post(
                    f"{BASE_URL}/api/logout", 
                    headers=headers, 
                    verify=False, 
                    timeout=10
                )
                
                print(f"[LOGOUT] Status: {response.status_code}")
                
                # Re-tester tous les tokens après logout
                print(f"\n[RETEST] Test des tokens après logout...")
                still_active = 0
                for token_info in active_tokens:
                    if self.test_token_validity(token_info):
                        still_active += 1
                
                if still_active == 0:
                    print(f"✅ Logout efficace: tous les tokens ont été révoqués")
                else:
                    print(f"⚠️ Logout partiel: {still_active} tokens encore actifs")
                    
            except Exception as e:
                print(f"[ERROR] Erreur lors du logout: {e}")
        
        print("\n" + "="*80)
        print("TEST TERMINÉ")
        print("="*80)

if __name__ == "__main__":
    print("Script de test de gestion des tokens JWT - API Simeo")
    print("Ce script va tester si les anciens tokens restent actifs")
    
    num_logins = input("\nNombre de connexions à tester (défaut: 3): ").strip()
    if not num_logins:
        num_logins = 3
    else:
        try:
            num_logins = int(num_logins)
        except:
            num_logins = 3
    
    input(f"\nLancement du test avec {num_logins} connexions. Appuyez sur Entrée pour continuer...")
    
    tester = JWTTokenTester()
    tester.run_token_management_test(num_logins)
