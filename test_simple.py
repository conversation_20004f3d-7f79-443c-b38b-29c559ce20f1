#!/usr/bin/env python3
"""
Test simple de connectivité API Simeo
"""

import requests
import json

BASE_URL = "https://phpstack-1478157-5631999.cloudwaysapps.com"
TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3BocHN0YWNrLTE0NzgxNTctNTYzMTk5OS5jbG91ZHdheXNhcHBzLmNvbS9hcGkvbG9naW4iLCJpYXQiOjE3NTE0NjQ5MTUsImV4cCI6MTc1MjY3NDUxNSwibmJmIjoxNzUxNDY0OTE1LCJqdGkiOiJIRmdIU3EyZjJYcjl3ZXl5Iiwic3ViIjoiMTA1NjIiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.az4Vbyqq0wAprxueMe9y1GG-TRRrDaG5urHDHOwHwo0"

def test_basic_connectivity():
    """Test de connectivité basique"""
    print("=== TEST DE CONNECTIVITÉ ===")
    
    # Test 1: Page d'accueil
    try:
        print(f"\n[TEST 1] GET {BASE_URL}/")
        response = requests.get(f"{BASE_URL}/", timeout=10, verify=False)
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content: {response.text[:200]}...")
    except Exception as e:
        print(f"Erreur: {e}")
    
    # Test 2: Endpoint API simple
    try:
        print(f"\n[TEST 2] GET {BASE_URL}/api/countries")
        response = requests.get(f"{BASE_URL}/api/countries", timeout=10, verify=False)
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content: {response.text[:200]}...")
    except Exception as e:
        print(f"Erreur: {e}")
    
    # Test 3: Endpoint avec authentification
    try:
        print(f"\n[TEST 3] GET {BASE_URL}/api/me")
        headers = {
            "Authorization": f"Bearer {TOKEN}",
            "Content-Type": "application/json"
        }
        response = requests.get(f"{BASE_URL}/api/me", headers=headers, timeout=10, verify=False)
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content: {response.text[:200]}...")
    except Exception as e:
        print(f"Erreur: {e}")

def test_mass_assignment_simple():
    """Test simple de mass assignment"""
    print("\n=== TEST MASS ASSIGNMENT SIMPLE ===")
    
    try:
        print(f"\n[TEST] POST {BASE_URL}/api/changeActiveZone")
        headers = {
            "Authorization": f"Bearer {TOKEN}",
            "Content-Type": "application/json"
        }
        data = {
            "zone": "Europe",
            "credit": 999999,
            "role": "admin"
        }
        
        print(f"Data envoyée: {json.dumps(data, indent=2)}")
        
        response = requests.post(
            f"{BASE_URL}/api/changeActiveZone", 
            json=data, 
            headers=headers, 
            timeout=10, 
            verify=False
        )
        
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content: {response.text}")
        
    except Exception as e:
        print(f"Erreur: {e}")

if __name__ == "__main__":
    print("Test simple API Simeo")
    print("=" * 50)
    
    # Désactiver les warnings SSL
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    test_basic_connectivity()
    test_mass_assignment_simple()
    
    print("\n" + "=" * 50)
    print("Tests terminés")
