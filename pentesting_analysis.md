# Analyse de Pentesting - Application Simeo

## 📋 Vue d'ensemble

Cette analyse couvre deux applications principales :
- **simeo-api** : API Laravel/Lumen pour la gestion des eSIM
- **cms-2025-01-01** : CMS WordPress pour l'administration

## 🎯 Endpoints API Simeo (Laravel/Lumen)

### 🔓 Endpoints Publics (Sans authentification)

#### Routes de développement/test
- `GET /begin` - Test de l'endpoint d'authentification
- `GET /list` - Catalogue complet des bundles disponibles

#### Routes publiques générales
- `GET /api/bundles` - Bundles par pays
- `GET /api/bundlesUnlimited` - Bundles illimités par pays
- `GET /api/bundlesRegion` - Bundles par région
- `GET /api/bundlesGo` - Bundles Go par pays
- `GET /api/countries` - Liste des pays
- `GET /api/getDiscounts` - Récupération des remises
- `GET /api/getNetworkByCountry` - Réseaux par pays
- `GET /api/esim/compatibleDevices` - Appareils compatibles eSIM

#### Routes d'authentification
- `POST /api/login` - Connexion utilisateur
- `POST /api/logout` - Déconnexion
- `POST /api/signUp` - Inscription
- `POST /api/confirmAccount` - Confirmation de compte
- `POST /api/forgotPassword` - Mot de passe oublié
- `POST /api/resetPassword` - Réinitialisation mot de passe

### 🔐 Endpoints Protégés (Authentification JWT requise)

#### Gestion utilisateur
- `GET /api/refresh` - Rafraîchissement token JWT
- `GET /api/me` - Informations utilisateur connecté
- `GET /api/userInfo` - Alias pour /api/me
- `POST /api/changePassword` - Changement de mot de passe
- `GET /api/deleteAccount` - Suppression de compte

#### Gestion eSIM
- `GET /api/esim/bundles` - Informations du bundle actif (params: iccID, distributorID)
- `GET /api/esim/all` - Toutes les eSIM et inventaire
- `GET /api/esim/qrCode` - QR Code de l'eSIM (param: distributorID)
- `GET /api/esim/sendQrCode` - Envoi QR Code par email (param: distributorID)
- `GET /api/esim/sendConfirmationSMS` - Envoi SMS de confirmation
- `GET /api/esim/getEsimInfoFromEsim` - Informations eSIM
- `POST /api/changeActiveZone` - Changement de zone active

#### Gestion des bundles
- `POST /api/bundles/validate` - Validation avant checkout
- `POST /api/bundles/apply` - Application depuis l'inventaire

#### Gestion des commandes
- `GET /api/orders/all` - Toutes les commandes

#### Gestion des parrainages
- `GET /api/referrals` - Récupération des parrainages
- `POST /api/changeReferralCode` - Changement code de parrainage

### 💳 Endpoints Stripe (Webhooks externes)
- `POST /stripePaymentIntent` - Webhook PaymentIntent
- `POST /stripeCheckout` - Webhook Checkout
- `POST /checkoutCredit` - Paiement de crédit Simeo

### 🔄 Callbacks externes
- `POST /api/callbackEsimGo` - Callback EsimGo

## 🌐 Endpoints WordPress CMS

### 🔧 AJAX Endpoints WordPress
- `wp-admin/admin-ajax.php?action=retrieve_client_infos` - Récupération infos client (POST)

### 📊 Pages d'administration
- `/wp-admin/` - Interface d'administration WordPress
- `/wp-admin/post.php` - Édition des posts (clients, commandes, eSIM)
- `/wp-admin/admin.php` - Pages d'administration personnalisées

## 🔗 Communication entre les applications

### Architecture de communication

1. **WordPress CMS → Base de données partagée**
   - Connexion directe à la base MySQL
   - Database: `kfgkkncdzz`
   - Username: `kfgkkncdzz`
   - Password: `WJS5PE9rZx`

2. **Laravel API → Base de données partagée**
   - Même base de données que WordPress
   - Gestion des utilisateurs, commandes, eSIM, inventaire

3. **Les deux applications → EsimGo API**
   - API Key: `jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X`
   - Base URL: `https://api.esim-go.com/v2.3/` et `https://api.esim-go.com/v2.4/`

4. **Laravel API → Stripe**
   - Clé de test: `sk_test_51PHrmBHcGR1hqoMIeBaSrphxIv3LdyJiXnMFHkRSld6ZqMVvmcEkdaRY25BN2PkExkWCtq29WMgQ4X1anr069zsq000p9JZb4P`
   - Clé de production: `***********************************************************************************************************`
   - Webhook secret: `whsec_nZwQenDKkoQYvTLIyZaJJ59klZY9KIDO`

## 🔒 Sécurité et Authentification

### JWT Authentication (Laravel)
- Guard: `api`
- Driver: `jwt`
- TTL: 14 jours (20160 minutes)
- Provider: Eloquent User model

### Middleware de sécurité
- `auth:api` - Authentification JWT
- CORS configuré avec headers:
  - `Access-Control-Allow-Origin: *`
  - `Access-Control-Allow-Methods: GET,HEAD,PUT,POST,DELETE,PATCH,OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, X-Auth-Token, Origin, Authorization`

### Endpoints sans authentification
- Routes publiques (bundles, pays, appareils compatibles)
- Routes d'authentification (login, signup, reset password)
- Webhooks Stripe
- Callbacks externes

## 🗄️ Base de données partagée

### Tables principales identifiées
- `users` - Utilisateurs (credit, stripe_customer_id, valid_account, active_zone, pass_token)
- `orders` - Commandes (id_order, product_name, total_paid, date, id_user)
- `esims` - Cartes eSIM (iccid, user_id)
- `inventories` - Inventaire (user_id)

### Connexion MySQL
- Host: `localhost`
- Database: `kfgkkncdzz`
- Username: `kfgkkncdzz`
- Password: `WJS5PE9rZx`

## 🔧 Services externes intégrés

### EsimGo API
- **Base URL**: `https://api.esim-go.com/v2.3/` et `v2.4/`
- **API Key**: `jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X`
- **Endpoints utilisés**:
  - `/esims/{iccID}/bundles` - Bundles d'une eSIM
  - `/esims/apply` - Application de bundle
  - `/esims/assignments/?reference={ref}` - QR Code
  - `/orders` - Création de commandes
  - `/inventory` - Inventaire
  - `/networks?isos={country}` - Réseaux par pays

### Stripe Payment
- **Environnement de test**: `sk_test_51PHrmBHcGR1hqoMI...`
- **Environnement de production**: `sk_live_51N0QkII934CqzGCu...`
- **Webhook secret**: `whsec_nZwQenDKkoQYvTLIyZaJJ59klZY9KIDO`
- **Monnaie**: CAD (Dollar canadien)

## ⚠️ Vulnérabilités potentielles identifiées

### 🔴 Critiques
1. **Credentials hardcodés**
   - API Key EsimGo exposée dans le code
   - Clés Stripe en dur dans le contrôleur
   - Credentials de base de données en clair

2. **CORS trop permissif**
   - `Access-Control-Allow-Origin: *` autorise tous les domaines

3. **Injection SQL potentielle**
   - Requêtes SQL construites avec concaténation dans WordPress
   - Exemple: `"SELECT * FROM esims WHERE iccid = '$iccID'"`

### 🟡 Moyennes
1. **Endpoints de développement en production**
   - `/begin` et `/list` semblent être des endpoints de test

2. **Gestion d'erreurs**
   - Certains endpoints peuvent révéler des informations sensibles

3. **Validation des entrées**
   - Validation limitée sur certains paramètres

### 🟢 Informations
1. **Architecture mixte**
   - WordPress et Laravel partagent la même base de données
   - Peut créer des incohérences de données

2. **Logs et monitoring**
   - Présence de logs mais niveau de détail à vérifier

## 📝 Recommandations de sécurité

1. **Externaliser les credentials**
   - Utiliser des variables d'environnement
   - Implémenter un gestionnaire de secrets

2. **Restreindre CORS**
   - Définir des domaines autorisés spécifiques

3. **Sécuriser les requêtes SQL**
   - Utiliser des requêtes préparées
   - Implémenter l'ORM Eloquent partout

4. **Supprimer les endpoints de développement**
   - Désactiver `/begin` et `/list` en production

5. **Améliorer la validation**
   - Valider tous les paramètres d'entrée
   - Implémenter des règles de validation strictes

6. **Monitoring et logging**
   - Implémenter un système de monitoring complet
   - Logger les tentatives d'accès non autorisées

## 🎯 Vecteurs d'attaque potentiels

### 1. Injection SQL (WordPress)
**Localisation**: `cms-2025-01-01/simeo/modules/admin/ajax/retrieve_client_infos.php`
```php
$query = "SELECT credit, stripe_customer_id, valid_account, active_zone, pass_token FROM users WHERE id = " . $_POST['client_id'];
```
**Impact**: Accès non autorisé à la base de données, extraction de données sensibles

### 2. Exposition de credentials
**Localisations multiples**:
- API Key EsimGo: `simeo-api/app/Helpers/getApiKey.php`
- Clés Stripe: `simeo-api/app/Http/Controllers/StripeController.php`
- DB credentials: `cms-2025-01-01/simeo/modules/configWordpress.php`

### 3. CORS mal configuré
**Impact**: Attaques Cross-Origin, vol de tokens JWT

### 4. Endpoints de développement exposés
- `/begin` - Peut révéler des informations sur l'authentification
- `/list` - Peut exposer le catalogue complet

### 5. Validation insuffisante des paramètres
**Exemples**:
- `client_id` dans AJAX WordPress (validation numérique basique)
- Paramètres d'URL non validés dans plusieurs endpoints

## 🔍 Tests de pénétration recommandés

### 1. Tests d'injection SQL
```bash
# Test sur l'endpoint AJAX WordPress
curl -X POST "http://target/wp-admin/admin-ajax.php" \
  -d "action=retrieve_client_infos&client_id=1' OR '1'='1"
```

### 2. Tests d'énumération d'utilisateurs
```bash
# Test sur l'endpoint de login
curl -X POST "http://target/api/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test"}'
```

### 3. Tests de bypass d'authentification
```bash
# Test sur les endpoints protégés sans token
curl -X GET "http://target/api/me"
curl -X GET "http://target/api/esim/all"
```

### 4. Tests de manipulation de paramètres
```bash
# Test sur les endpoints avec paramètres
curl -X GET "http://target/api/esim/bundles?iccID=../../../etc/passwd"
```

## 📊 Matrice de risques

| Vulnérabilité | Probabilité | Impact | Risque Global |
|---------------|-------------|---------|---------------|
| Injection SQL | Élevée | Critique | **CRITIQUE** |
| Credentials exposés | Élevée | Critique | **CRITIQUE** |
| CORS permissif | Moyenne | Élevé | **ÉLEVÉ** |
| Endpoints de dev | Faible | Moyen | **MOYEN** |
| Validation faible | Moyenne | Moyen | **MOYEN** |

## 🛠️ Outils recommandés pour les tests

### Scanners automatisés
- **SQLMap** - Pour les injections SQL
- **Burp Suite** - Proxy d'interception et scanner
- **OWASP ZAP** - Scanner de sécurité web
- **Nikto** - Scanner de vulnérabilités web

### Tests manuels
- **Postman/Insomnia** - Tests d'API
- **curl** - Tests en ligne de commande
- **Browser Developer Tools** - Analyse côté client

### Tests spécialisés
- **JWT.io** - Analyse des tokens JWT
- **Stripe CLI** - Tests des webhooks Stripe
- **MySQL clients** - Tests de base de données

## 📋 Checklist de sécurité

### ✅ Tests à effectuer

#### Authentification et autorisation
- [ ] Test de bypass d'authentification JWT
- [ ] Test d'expiration des tokens
- [ ] Test de manipulation des tokens
- [ ] Test d'élévation de privilèges

#### Injection et validation
- [ ] Test d'injection SQL sur tous les endpoints
- [ ] Test d'injection NoSQL (si applicable)
- [ ] Test de validation des paramètres
- [ ] Test de débordement de buffer

#### Configuration et exposition
- [ ] Test de divulgation d'informations sensibles
- [ ] Test de configuration CORS
- [ ] Test d'exposition de fichiers sensibles
- [ ] Test de headers de sécurité

#### Logique métier
- [ ] Test de manipulation de prix
- [ ] Test de bypass de paiement
- [ ] Test de manipulation d'inventaire
- [ ] Test de logique de parrainage

## 🚨 Actions immédiates recommandées

### Priorité 1 (Critique)
1. **Corriger les injections SQL**
   - Implémenter des requêtes préparées
   - Valider tous les paramètres d'entrée

2. **Sécuriser les credentials**
   - Déplacer vers des variables d'environnement
   - Utiliser un gestionnaire de secrets

3. **Restreindre CORS**
   - Définir des domaines autorisés spécifiques
   - Implémenter une whitelist

### Priorité 2 (Élevée)
1. **Supprimer les endpoints de développement**
2. **Améliorer la validation des entrées**
3. **Implémenter des headers de sécurité**

### Priorité 3 (Moyenne)
1. **Améliorer le logging et monitoring**
2. **Implémenter des tests de sécurité automatisés**
3. **Documenter les procédures de sécurité**

---

*Analyse générée le 2025-07-02 pour l'audit de sécurité de l'application Simeo*
