console.log('esims.js');

// Always remove parameter "refund=1" from the URL
jQuery(document).ready(function($) {
    setTimeout(function() {
        var url = new URL(window.location.href);
        url.searchParams.delete('revoke');
        url.searchParams.delete('bundleName');
        url.searchParams.delete('assignmentId');
        url.searchParams.delete('deleteEsim');
        window.history.replaceState({}, document.title, url.toString());
    }, 2000);
});

// Revoke bundle
// Display validation popup before revoke with Yes/No
jQuery('.revoke-bundle').click(function(event) {
    var confirm = window.confirm('Voulez-vous révoquer ce bundle ?');
    if(!confirm) {
        event.preventDefault();
    } else {
        window.location.href = jQuery(this).attr('href');
    }
});

// Delete eSIM
jQuery('.delete-esim').click(function(event) {
    var userInput = window.prompt('Pour confirmer la suppression de l\'eSIM, veuillez entrer "DELETE" :');
    if(userInput !== 'DELETE') {
        event.preventDefault();
        alert('Suppression annulée. Vous devez entrer "DELETE" pour confirmer.');
    } else {
        window.location.href = jQuery(this).attr('href');
    }
});

