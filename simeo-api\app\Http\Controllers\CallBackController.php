<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\BundleController;
use App\Http\Controllers\eSimController;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use App\Services\EsimGoService;
use Illuminate\Support\Facades\Log;

class CallBackController extends Controller
{


    public function __construct(BundleController $BundleController, eSimController $eSimController, EsimGoService $eSimGoService)
    {
        $this->middleware('auth:api', ['except' => ['callbackEsimGo']]);
        $this->bundleController = $BundleController;
        $this->eSimController = $eSimController;
        $this->eSimGoService = $eSimGoService;
    }
    /**
     * Get a JWT via given credentials.
     *
     * @param  Request  $request
     * @return Response
     */
    public function callbackEsimGo(Request $request)
    {
        $data = $request->all();
        
        // Alert Type
        $sendMail = false;
        $sendSMS = false;
        $message = "Aucun message à afficher";
        if(isset($data) && isset($data['alertType'])){
            $alertType = $data['alertType'];
            $iccID = $data['iccid'];
            \Illuminate\Support\Facades\Log::info("Alert Type: " . $alertType);
            \Illuminate\Support\Facades\Log::info("ICCID: " . $iccID);
            
            if($alertType == 'Utilisation'){
                $sendMail = false;
                $sendSMS = true;
                \Illuminate\Support\Facades\Log::info("Données complètes reçues : " . print_r($data, true));
                if(isset($data['bundle']['initialQuantity']) && isset($data['bundle']['remainingQuantity'])){
                    $initialQuantity = $data['bundle']['initialQuantity'];
                    $remainingQuantity = $data['bundle']['remainingQuantity'];
                    $id_bundle = $data['bundle']['id'];

                    
                    // Vérification pour éviter la division par zéro
                    if($initialQuantity <= 0){
                        \Illuminate\Support\Facades\Log::warning("Initial quantity is zero or negative: " . $initialQuantity);
                        return response()->json([
                            'status' => 'error',
                            'msg' => 'Initial quantity is zero or negative'
                        ], 401);
                    }else{
                        $percentage = (($initialQuantity - $remainingQuantity) / $initialQuantity) * 100;
                    }

                    \Illuminate\Support\Facades\Log::info("Bundle: " . print_r($data['bundle'], true));
                    \Illuminate\Support\Facades\Log::info("Pourcentage d'utilisation: " . $percentage);
                    \Illuminate\Support\Facades\Log::info("Initial Quantity: " . $initialQuantity);
                    \Illuminate\Support\Facades\Log::info("Remaining Quantity: " . $remainingQuantity);

                    $notificationType = '';
                    if($percentage >= 95){
                        $message = "Plus que 5 % de données sur ta eSIM ! Ouvre l'application Simeo://mes-forfaits pour recharger et rester connecté.";
                        $notificationType = '5_percent';
                        \Illuminate\Support\Facades\Log::info("Notification 5% déclenchée");
                    }else if($percentage >= 80){
                        $message = "20 % restants sur ta eSIM : recharge vite ton forfait sur l'application Simeo://mes-forfaits pour éviter toute coupure.";
                        $notificationType = '20_percent';
                        \Illuminate\Support\Facades\Log::info("Notification 20% déclenchée");
                    }else if($remainingQuantity <= 0){
                        $message = "Données épuisées sur ta eSIM! Recharge ton forfait sur l'application Simeo://mes-forfaits pour reprendre la connexion.";
                        $notificationType = '0_percent';
                        \Illuminate\Support\Facades\Log::info("Notification 0% déclenchée");
                    }else{
                        $sendMail = false;
                        $sendSMS = false;
                        \Illuminate\Support\Facades\Log::info("Aucune notification déclenchée - Pourcentage: " . $percentage);
                    }

                    // Vérifier si cette notification a déjà été envoyée pour ce bundle
                    if($sendSMS && !empty($notificationType)){
                        $dateSent = date('Ymd');
                        $existingNotification = DB::select("SELECT id FROM notifications_history WHERE iccid = ? AND id_bundle = ? AND notificationType = ? AND dateSent = ?", [$iccID, $id_bundle, $notificationType, $dateSent]);
                        
                        if(count($existingNotification) > 0){
                            \Illuminate\Support\Facades\Log::info("Notification déjà envoyée pour " . $notificationType . " - ICCID: " . $iccID . " - Bundle: " . $id_bundle);
                            $sendSMS = false;
                        }
                    }
                    //$message .= " - " . $iccID;
                    \Illuminate\Support\Facades\Log::info("Message à envoyer: " . $message);
                }else{
                    $message = "Aucun message à afficher";
                    $sendMail = false;
                    $sendSMS = false;
                }
            }else{
                $alertType = "Unknown";
                $iccID = "Unknown";
            }
        }
        
        
        // Récupérer la signature du header
        $signatureHeader = $request->header('x-signature-sha256');
        
        // Créer la signature HMAC
        $key = getApiKey_eSimGo();
        
        $body = $request->getContent(); // Récupère le corps brut exact de la requête
        if($body == null){
            $body = json_encode($data);
        }
        
        $signature = base64_encode(hash_hmac('sha256', $body, $key, true));
        
        // Vérifier si les signatures correspondent
        if($signatureHeader == null){
            $signatureHeader = "";
        }
        
        $matches = hash_equals($signature, $signatureHeader);
        
        if (!$matches) {
            /*$from = "<EMAIL>";
            $to = "<EMAIL>";
            $subject = "callbackEsimGo - Signature invalide";
            $message = print_r($data, true) . "\r\n" . $message;
            
            $headers = "From: " . $from . "\r\n";
            $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
            $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
            $headers .= "X-Priority: 1\r\n";
            $headers .= "X-MS-Priority: High\r\n";
            $headers .= "X-MS-TNEF: BODY=8BITMIME\r\n";
            $headers .= "X-MS-ExchangeServer: 15.0.0.0\r\n";
            mail($to, $subject, $message, $headers);*/
            return response()->json([
                'status' => 'error',
                'msg' => 'Signature invalide'
            ], 401);
        }

        if($sendSMS){
            // Sécurité : tronque le message à 160 caractères maximum
            $iccID = "8943108165016274354";

            if(mb_strlen($message) > 160){
                $message = mb_substr($message, 0, 160);
            }

            $smsResult = $this->eSimGoService->sendAlertSMS($iccID, $message);
            
            \Illuminate\Support\Facades\Log::info("Tentative d'envoi de SMS - ICCID: " . $iccID . " - Message: " . $message);
            
            // Enregistrer la notification dans l'historique si l'envoi a réussi
            if($smsResult && isset($notificationType) && !empty($notificationType) && isset($id_bundle) && isset($percentage)){
                DB::insert("INSERT INTO notifications_history (id_bundle, iccid, notificationType) VALUES (?, ?, ?)", 
                    [$id_bundle, $data['iccid'], $notificationType]);
                
                \Illuminate\Support\Facades\Log::info("Notification enregistrée dans l'historique - Type: " . $notificationType . " - ICCID: " . $data['iccid'] . " - Bundle: " . $id_bundle);
            } else {
                \Illuminate\Support\Facades\Log::warning("Échec de l'envoi du SMS ou données manquantes - ICCID: " . $data['iccid']);
            }
        }

        if($sendMail){
            $from = "<EMAIL>";
            $to = "<EMAIL>";
            $subject = "callbackEsimGo - " . $alertType . " - " . $iccID;
            $message = print_r($data, true) . "\r\n" . $message;
            $headers = "From: " . $from . "\r\n";
            $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
            $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
            $headers .= "X-Priority: 1\r\n";
            $headers .= "X-MS-Priority: High\r\n";
            $headers .= "X-MS-TNEF: BODY=8BITMIME\r\n";
            $headers .= "X-MS-ExchangeServer: 15.0.0.0\r\n";
            mail($to, $subject, $message, $headers);
        }

        return [
            'status' => 'success',
            'msg' => "Callback received"
        ];
    }

}
