console.log('clients.js');

jQuery(document).ready(function($) {
    console.log('clients.js ready');

    setTimeout(function() {
        var url = new URL(window.location.href);
        url.searchParams.delete('deletedUserEsim');
        url.searchParams.delete('validAccount');
        window.history.replaceState({}, document.title, url.toString());
    }, 2000);

    // read-only input
    jQuery('div[data-name="client_id"] input').prop('readonly', true);
    jQuery('div[data-name="courriel"] input').prop('readonly', true);
    jQuery('div[data-name="date_dinscription"] input').prop('readonly', true);
    jQuery('div[data-name="stripe_customer_id"] input').prop('readonly', true);
    jQuery('div[data-name="passcode"] input').prop('readonly', true);


    // Retrieve client infos
    var client_id = jQuery('div[data-name="client_id"] input').val();
    var data = {
        'action': 'retrieve_client_infos',
        'client_id': client_id
    };

    jQuery.post(ajaxurl, data, function(response) {
        response = JSON.parse(response);
        var userInfo = JSON.parse(response.userInfo);
        jQuery('div[data-name="credits"] input').val(userInfo.credit);
        jQuery('div[data-name="stripe_customer_id"] input').val(userInfo.stripe_customer_id);
        if(userInfo.pass_token != 0){
            jQuery('div[data-name="passcode"] input').val(userInfo.pass_token);
        }else{
            jQuery('div[data-name="passcode"] input').val('N/D');
        }
        //console.log(response);

        // Display inventory
        var inventory = JSON.parse(response.inventory);
        display_inventory(inventory);

        // Display esims
        var esims = JSON.parse(response.esims);
        display_esims(esims);

        // Display orders
        //display_orders(response.orders);
        var orders = JSON.parse(response.orders);
        display_orders(orders);
    });
});

function display_esims(esims) {
// Display esims
    var html_esims = '<table class="table table-striped">';
    html_esims += '<tr><th>ICCID</th><th>Zone</th><th>Date d\'application</th></tr>';
    for(var i = 0; i < esims.length; i++) {
        html_esims += '<tr><td><a href="/wp-admin/post.php?post=' + esims[i].post_id + '&action=edit"><strong>' + esims[i].iccid + '</strong></a></td><td>' + esims[i].zone + '</td><td>' + esims[i].last_apply_date + '</td></tr>';
    }
    html_esims += '</table>';
    jQuery('#esims_client').find('.inside').html(html_esims);
}

function display_orders(orders) {
    var html_orders = '<table class="table table-striped">';
    html_orders += '<tr><th>Commande</th><th>Produit</th><th>Prix</th></tr>';
    for(var i = 0; i < orders.length; i++) {
        html_orders += '<tr><td><a href="/wp-admin/post.php?post=' + orders[i].post_id + '&action=edit"><strong>' + orders[i].id_order + '</strong></a></td><td>' + orders[i].product_name.replace('eSIM - ', '') + '</td><td>' + orders[i].total_paid + '</td></tr>';
    }
    html_orders += '</table>';
    jQuery('#orders_client').find('.inside').html(html_orders);
}

function display_inventory(inventory) {
    var html_inventory = '<table class="table table-striped">';
    html_inventory += '<tr><th>Produit</th><th>Action</th></tr>';
    for(var i = 0; i < inventory.length; i++) {
        var product_name = inventory[i].bundleName;
        var postID = jQuery('#post_ID').val();
        var action_revoke_button = '<a href="/wp-admin/post.php?post=' + postID + '&action=edit&revokeInventory=1&inventoryId=' + inventory[i].id + '" class="btn-red-simeo revoke-inventory">Revoke</a>';
        if(product_name.includes('eSIM')){
            product_name = product_name.replace('eSIM - ', '');
        }
        html_inventory += '<tr><td>' + product_name + '</td><td>' + action_revoke_button + '</td></tr>';
    }
    html_inventory += '</table>';
    jQuery('#inventory_client').find('.inside').html(html_inventory);
}

// Revoke inventory
jQuery(document).ready(function($) {
    jQuery('body').on('click', '.revoke-inventory', function(event) {
        event.preventDefault();
        var confirm = window.confirm('Voulez-vous révoquer ce bundle en inventaire ?');
        if(!confirm) {
            event.preventDefault();
        } else {
            window.location.href = jQuery(this).attr('href');
        }
    });

    // Always remove parameter "revokeInventory=1" from the URL
    jQuery(document).ready(function($) {
        setTimeout(function() {
            var url = new URL(window.location.href);
            url.searchParams.delete('revokeInventory');
            url.searchParams.delete('inventoryId');
            window.history.replaceState({}, document.title, url.toString());
        }, 2000);
    });
});