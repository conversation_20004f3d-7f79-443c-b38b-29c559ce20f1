$wpscan --url https://wordpress-1478157-5648437.cloudwaysapps.com --random-user-agent
_______________________________________________________________
         __          _______   _____
         \ \        / /  __ \ / ____|
          \ \  /\  / /| |__) | (___   ___  __ _ _ __ ®
           \ \/  \/ / |  ___/ \___ \ / __|/ _` | '_ \
            \  /\  /  | |     ____) | (__| (_| | | | |
             \/  \/   |_|    |_____/ \___|\__,_|_| |_|

         WordPress Security Scanner by the WPScan Team
                         Version 3.8.28
                               
       @_WPScan_, @ethicalhack3r, @erwan_lr, @firefart
_______________________________________________________________

[i] Updating the Database ...
[i] Update completed.

[+] URL: https://wordpress-1478157-5648437.cloudwaysapps.com/ [159.203.35.206]
[+] Started: Sun Jul  6 21:27:26 2025

Interesting Finding(s):

[+] Headers
 | Interesting Entries:
 |  - server: nginx
 |  - cache-provider: CLOUDWAYS-CACHE-DE
 |  - x-robots-tag: noindex, nofollow
 | Found By: Headers (Passive Detection)
 | Confidence: 100%

[+] robots.txt found: https://wordpress-1478157-5648437.cloudwaysapps.com/robots.txt
 | Interesting Entries:
 |  - /wp-admin/
 |  - /wp-admin/admin-ajax.php
 | Found By: Robots Txt (Aggressive Detection)
 | Confidence: 100%

[+] WordPress readme found: https://wordpress-1478157-5648437.cloudwaysapps.com/readme.html
 | Found By: Direct Access (Aggressive Detection)
 | Confidence: 100%

[+] The external WP-Cron seems to be enabled: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-cron.php
 | Found By: Direct Access (Aggressive Detection)
 | Confidence: 60%
 | References:
 |  - https://www.iplocation.net/defend-wordpress-from-ddos
 |  - https://github.com/wpscanteam/wpscan/issues/1299

[+] WordPress version 6.8.1 identified (Latest, released on 2025-04-30).
 | Found By: Emoji Settings (Passive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/, Match: 'wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1'
 | Confirmed By: Meta Generator (Passive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/, Match: 'WordPress 6.8.1'

[+] WordPress theme in use: simeo
 | Location: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/themes/simeo/
 | Style URL: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/themes/simeo/style.css?ver=6.8.1
 | Style Name: Simeo
 | Style URI: https://simeo.ca/
 | Description: Simeo CMS...
 | Author: Simeo
 | Author URI: https://simeo.ca/
 |
 | Found By: Css Style In Homepage (Passive Detection)
 | Confirmed By: Css Style In 404 Page (Passive Detection)
 |
 | Version: 1.0 (80% confidence)
 | Found By: Style (Passive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/themes/simeo/style.css?ver=6.8.1, Match: 'Version: 1.0'

[+] Enumerating All Plugins (via Passive Methods)
[+] Checking Plugin Versions (via Passive and Aggressive Methods)

[i] Plugin(s) Identified:

[+] *
 | Location: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/plugins/*/
 |
 | Found By: Urls In Homepage (Passive Detection)
 | Confirmed By: Urls In 404 Page (Passive Detection)
 |
 | The version could not be determined.

[+] breeze
 | Location: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/plugins/breeze/
 | Last Updated: 2025-06-18T07:47:00.000Z
 | [!] The version is out of date, the latest version is 2.2.14
 |
 | Found By: Urls In Homepage (Passive Detection)
 | Confirmed By: Urls In 404 Page (Passive Detection)
 |
 | Version: 2.2.1 (80% confidence)
 | Found By: Readme - Stable Tag (Aggressive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/plugins/breeze/readme.txt

[+] Enumerating Config Backups (via Passive and Aggressive Methods)
 Checking Config Backups - Time: 00:00:03 <=> (137 / 137) 100.00% Time: 00:00:03

[i] No Config Backups Found.

[!] No WPScan API Token given, as a result vulnerability data has not been output.
[!] You can get a free API token with 25 daily requests by registering at https://wpscan.com/register

[+] Finished: Sun Jul  6 21:27:38 2025
[+] Requests Done: 192
[+] Cached Requests: 7
[+] Data Sent: 68.912 KB
[+] Data Received: 22.328 MB
[+] Memory used: 283.73 MB
[+] Elapsed time: 00:00:11




$wpscan --url https://wordpress-1478157-5648437.cloudwaysapps.com --enumerate u --random-user-agent
_______________________________________________________________
         __          _______   _____
         \ \        / /  __ \ / ____|
          \ \  /\  / /| |__) | (___   ___  __ _ _ __ ®
           \ \/  \/ / |  ___/ \___ \ / __|/ _` | '_ \
            \  /\  /  | |     ____) | (__| (_| | | | |
             \/  \/   |_|    |_____/ \___|\__,_|_| |_|

         WordPress Security Scanner by the WPScan Team
                         Version 3.8.28
       Sponsored by Automattic - https://automattic.com/
       @_WPScan_, @ethicalhack3r, @erwan_lr, @firefart
_______________________________________________________________

[+] URL: https://wordpress-1478157-5648437.cloudwaysapps.com/ [159.203.35.206]
[+] Started: Sun Jul  6 22:06:33 2025

Interesting Finding(s):

[+] Headers
 | Interesting Entries:
 |  - server: nginx
 |  - cache-provider: CLOUDWAYS-CACHE-DE
 |  - x-robots-tag: noindex, nofollow
 | Found By: Headers (Passive Detection)
 | Confidence: 100%

[+] robots.txt found: https://wordpress-1478157-5648437.cloudwaysapps.com/robots.txt
 | Interesting Entries:
 |  - /wp-admin/
 |  - /wp-admin/admin-ajax.php
 | Found By: Robots Txt (Aggressive Detection)
 | Confidence: 100%

[+] WordPress readme found: https://wordpress-1478157-5648437.cloudwaysapps.com/readme.html
 | Found By: Direct Access (Aggressive Detection)
 | Confidence: 100%

[+] The external WP-Cron seems to be enabled: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-cron.php
 | Found By: Direct Access (Aggressive Detection)
 | Confidence: 60%
 | References:
 |  - https://www.iplocation.net/defend-wordpress-from-ddos
 |  - https://github.com/wpscanteam/wpscan/issues/1299

[+] WordPress version 6.8.1 identified (Latest, released on 2025-04-30).
 | Found By: Emoji Settings (Passive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/, Match: 'wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1'
 | Confirmed By: Meta Generator (Passive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/, Match: 'WordPress 6.8.1'

[+] WordPress theme in use: simeo
 | Location: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/themes/simeo/
 | Style URL: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/themes/simeo/style.css?ver=6.8.1
 | Style Name: Simeo
 | Style URI: https://simeo.ca/
 | Description: Simeo CMS...
 | Author: Simeo
 | Author URI: https://simeo.ca/
 |
 | Found By: Css Style In Homepage (Passive Detection)
 | Confirmed By: Css Style In 404 Page (Passive Detection)
 |
 | Version: 1.0 (80% confidence)
 | Found By: Style (Passive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/themes/simeo/style.css?ver=6.8.1, Match: 'Version: 1.0'

[+] Enumerating Users (via Passive and Aggressive Methods)
 Brute Forcing Author IDs - Time: 00:00:01 <==> (10 / 10) 100.00% Time: 00:00:01

[i] User(s) Identified:

[+] geremy
 | Found By: Wp Json Api (Aggressive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/wp-json/wp/v2/users/?per_page=100&page=1
 | Confirmed By: Author Id Brute Forcing - Author Pattern (Aggressive Detection)

[+] Geremy
 | Found By: Rss Generator (Aggressive Detection)

[+] jeremy
 | Found By: Author Id Brute Forcing - Author Pattern (Aggressive Detection)
 | Confirmed By: Login Error Messages (Aggressive Detection)

[+] geremy-turcottehotmail-com
 | Found By: Author Id Brute Forcing - Author Pattern (Aggressive Detection)

[!] No WPScan API Token given, as a result vulnerability data has not been output.
[!] You can get a free API token with 25 daily requests by registering at https://wpscan.com/register

[+] Finished: Sun Jul  6 22:06:40 2025
[+] Requests Done: 56
[+] Cached Requests: 7
[+] Data Sent: 18.835 KB
[+] Data Received: 252.041 KB
[+] Memory used: 184.031 MB
[+] Elapsed time: 00:00:07



$wpscan --url https://wordpress-1478157-5648437.cloudwaysapps.com --enumerate vp --random-user-agent
_______________________________________________________________
         __          _______   _____
         \ \        / /  __ \ / ____|
          \ \  /\  / /| |__) | (___   ___  __ _ _ __ ®
           \ \/  \/ / |  ___/ \___ \ / __|/ _` | '_ \
            \  /\  /  | |     ____) | (__| (_| | | | |
             \/  \/   |_|    |_____/ \___|\__,_|_| |_|

         WordPress Security Scanner by the WPScan Team
                         Version 3.8.28
       Sponsored by Automattic - https://automattic.com/
       @_WPScan_, @ethicalhack3r, @erwan_lr, @firefart
_______________________________________________________________

[+] URL: https://wordpress-1478157-5648437.cloudwaysapps.com/ [159.203.35.206]
[+] Started: Sun Jul  6 22:07:20 2025

Interesting Finding(s):

[+] Headers
 | Interesting Entries:
 |  - server: nginx
 |  - cache-provider: CLOUDWAYS-CACHE-DE
 |  - x-robots-tag: noindex, nofollow
 | Found By: Headers (Passive Detection)
 | Confidence: 100%

[+] robots.txt found: https://wordpress-1478157-5648437.cloudwaysapps.com/robots.txt
 | Interesting Entries:
 |  - /wp-admin/
 |  - /wp-admin/admin-ajax.php
 | Found By: Robots Txt (Aggressive Detection)
 | Confidence: 100%

[+] WordPress readme found: https://wordpress-1478157-5648437.cloudwaysapps.com/readme.html
 | Found By: Direct Access (Aggressive Detection)
 | Confidence: 100%

[+] The external WP-Cron seems to be enabled: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-cron.php
 | Found By: Direct Access (Aggressive Detection)
 | Confidence: 60%
 | References:
 |  - https://www.iplocation.net/defend-wordpress-from-ddos
 |  - https://github.com/wpscanteam/wpscan/issues/1299

[+] WordPress version 6.8.1 identified (Latest, released on 2025-04-30).
 | Found By: Emoji Settings (Passive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/, Match: 'wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1'
 | Confirmed By: Meta Generator (Passive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/, Match: 'WordPress 6.8.1'

[+] WordPress theme in use: simeo
 | Location: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/themes/simeo/
 | Style URL: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/themes/simeo/style.css?ver=6.8.1
 | Style Name: Simeo
 | Style URI: https://simeo.ca/
 | Description: Simeo CMS...
 | Author: Simeo
 | Author URI: https://simeo.ca/
 |
 | Found By: Css Style In Homepage (Passive Detection)
 | Confirmed By: Css Style In 404 Page (Passive Detection)
 |
 | Version: 1.0 (80% confidence)
 | Found By: Style (Passive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/themes/simeo/style.css?ver=6.8.1, Match: 'Version: 1.0'

[+] Enumerating Vulnerable Plugins (via Passive Methods)
[+] Checking Plugin Versions (via Passive and Aggressive Methods)

[i] No plugins Found.

[!] No WPScan API Token given, as a result vulnerability data has not been output.
[!] You can get a free API token with 25 daily requests by registering at https://wpscan.com/register

[+] Finished: Sun Jul  6 22:07:26 2025
[+] Requests Done: 39
[+] Cached Requests: 7
[+] Data Sent: 12.828 KB
[+] Data Received: 139.019 KB
[+] Memory used: 273.121 MB
[+] Elapsed time: 00:00:05



$wpscan --url https://wordpress-1478157-5648437.cloudwaysapps.com --enumerate vt --random-user-agent
_______________________________________________________________
         __          _______   _____
         \ \        / /  __ \ / ____|
          \ \  /\  / /| |__) | (___   ___  __ _ _ __ ®
           \ \/  \/ / |  ___/ \___ \ / __|/ _` | '_ \
            \  /\  /  | |     ____) | (__| (_| | | | |
             \/  \/   |_|    |_____/ \___|\__,_|_| |_|

         WordPress Security Scanner by the WPScan Team
                         Version 3.8.28
       Sponsored by Automattic - https://automattic.com/
       @_WPScan_, @ethicalhack3r, @erwan_lr, @firefart
_______________________________________________________________

[+] URL: https://wordpress-1478157-5648437.cloudwaysapps.com/ [159.203.35.206]
[+] Started: Sun Jul  6 22:07:51 2025

Interesting Finding(s):

[+] Headers
 | Interesting Entries:
 |  - server: nginx
 |  - cache-provider: CLOUDWAYS-CACHE-DE
 |  - x-robots-tag: noindex, nofollow
 | Found By: Headers (Passive Detection)
 | Confidence: 100%

[+] robots.txt found: https://wordpress-1478157-5648437.cloudwaysapps.com/robots.txt
 | Interesting Entries:
 |  - /wp-admin/
 |  - /wp-admin/admin-ajax.php
 | Found By: Robots Txt (Aggressive Detection)
 | Confidence: 100%

[+] WordPress readme found: https://wordpress-1478157-5648437.cloudwaysapps.com/readme.html
 | Found By: Direct Access (Aggressive Detection)
 | Confidence: 100%

[+] The external WP-Cron seems to be enabled: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-cron.php
 | Found By: Direct Access (Aggressive Detection)
 | Confidence: 60%
 | References:
 |  - https://www.iplocation.net/defend-wordpress-from-ddos
 |  - https://github.com/wpscanteam/wpscan/issues/1299

[+] WordPress version 6.8.1 identified (Latest, released on 2025-04-30).
 | Found By: Emoji Settings (Passive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/, Match: 'wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1'
 | Confirmed By: Meta Generator (Passive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/, Match: 'WordPress 6.8.1'

[+] WordPress theme in use: simeo
 | Location: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/themes/simeo/
 | Style URL: https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/themes/simeo/style.css?ver=6.8.1
 | Style Name: Simeo
 | Style URI: https://simeo.ca/
 | Description: Simeo CMS...
 | Author: Simeo
 | Author URI: https://simeo.ca/
 |
 | Found By: Css Style In Homepage (Passive Detection)
 | Confirmed By: Css Style In 404 Page (Passive Detection)
 |
 | Version: 1.0 (80% confidence)
 | Found By: Style (Passive Detection)
 |  - https://wordpress-1478157-5648437.cloudwaysapps.com/wp-content/themes/simeo/style.css?ver=6.8.1, Match: 'Version: 1.0'

[+] Enumerating Vulnerable Themes (via Passive and Aggressive Methods)
 Checking Known Locations - Time: 00:00:31 <> (652 / 652) 100.00% Time: 00:00:31
[+] Checking Theme Versions (via Passive and Aggressive Methods)

[i] No themes Found.

[!] No WPScan API Token given, as a result vulnerability data has not been output.
[!] You can get a free API token with 25 daily requests by registering at https://wpscan.com/register

[+] Finished: Sun Jul  6 22:08:26 2025
[+] Requests Done: 685
[+] Cached Requests: 9
[+] Data Sent: 243.248 KB
[+] Data Received: 326.155 KB
[+] Memory used: 208.957 MB
[+] Elapsed time: 00:00:35
