<?php


// Removes from admin bar
function mytheme_admin_bar_render() {
    global $wp_admin_bar;
    $wp_admin_bar->remove_menu('comments');
}
add_action( 'wp_before_admin_bar_render', 'mytheme_admin_bar_render' );

// Removes from admin menu
add_action( 'admin_menu', 'my_remove_admin_menus' );
function my_remove_admin_menus() {
    remove_menu_page( 'edit-comments.php' );
    remove_menu_page( 'edit.php' );
}

function simeo_theme_enqueue_scripts() {

    wp_enqueue_style('style', get_stylesheet_uri());

}
add_action('wp_enqueue_scripts', 'simeo_theme_enqueue_scripts');

function simeo_admin_style_enqueue() {
    wp_enqueue_style('style', get_template_directory_uri() . '/style.css', array(), time(), 'all');
}
add_action('admin_enqueue_scripts', 'simeo_admin_style_enqueue');

function simeo_theme_setup() {

    add_theme_support('post-thumbnails');

    add_theme_support('custom-logo');

    register_nav_menus(array(
        'primary' => __('Primary Menu'),
    ));

}
add_action('after_setup_theme', 'simeo_theme_setup');

function connect_to_datacenter() {
    $DATABASE = 'kfgkkncdzz';
    $USERNAME = 'kfgkkncdzz';
    $PASSWORD = 'WJS5PE9rZx';
    $conn = new mysqli('localhost', $USERNAME, $PASSWORD, $DATABASE);

    if ($conn->connect_error) {
        return 0;
    }else{
        return $conn;
    }
}

function get_iso_if_region($region_name) {
    if($region_name == 'Africa') {
        return 'RAF';
    }

    if($region_name == 'Asia') {
        return 'RAS';
    }

    if($region_name == 'Europe+') {
        return 'REUP';
    }

    if($region_name == 'Caribbean') {
        return 'RCA';
    }

    if($region_name == 'Oceania') {
        return 'OCE';
    }

    if($region_name == 'LATAM') {
        return 'ROC';
    }

    if($region_name == 'Middle East') {
        return 'RME';
    }

    if($region_name == 'North America') {
        return 'RNA';
    }

    return $region_name;
}

function get_region_from_iso($iso) {
    if($iso == 'RAF') {
        return 'Africa';
    }

    if($iso == 'RAS') {
        return 'Asia';
    }

    if($iso == 'REUP') {
        return 'Europe+';
    }

    if($iso == 'RCA') {
        return 'Caribbean';
    }

    if($iso == 'OCE') {
        return 'Oceania';
    }

    if($iso == 'ROC') {
        return 'LATAM';
    }

    if($iso == 'RME') {
        return 'Middle East';
    }

    if($iso == 'RNA') {
        return 'North America';
    }

    return $iso;
}
?>