<?php

namespace App\Http\Controllers;

class ListController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    public function catalogue(){

        $params = array(
            'page' => '1',
            'perPage' => '100',
            'direction' => 'asc',
            'orderBy' => 'bundleName'
        );

        //Create string POST
        $paramString = "";
        foreach($params as $name => $value){
            $paramString .= $name . "=" . $value . "&";
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/catalogue?perPage=100000');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X'
        ]);
        //curl_setopt($ch, CURLOPT_POSTFIELDS, trim($paramString, '?'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_NOBODY, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $res = curl_exec($ch);
        curl_close($ch);


        $results_obj = json_decode($res);
        echo count($results_obj->bundles) . "<br/>";
        foreach($results_obj->bundles as $bundle){
            unset($bundle->roamingEnabled);
            /*if($bundle->countries[0]->name == 'Mexico'){
                echo "-----------" . "<br/>";
                echo $bundle->name . "<br/>";
                echo $bundle->description . "<br/>";
                echo $bundle->countries[0]->name . "<br/>";
                echo $bundle->dataAmount . " Mo<br/>";
                echo number_format($bundle->price, 2, '.', '') . "$<br/>";
            }*/

            echo "<pre>";
            var_dump($bundle);
            echo "</pre>";
            //var_dump($bundle);
           // die;
        }

        

    }

    //
}
