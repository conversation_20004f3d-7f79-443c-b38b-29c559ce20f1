import base64
import json
import sys

def decode_jwt(token):
    parts = token.strip().split('.')
    if len(parts) != 3:
        raise ValueError("Token malformed")
    header_b64, payload_b64, signature = parts

    def b64_decode(segment):
        segment += '=' * (-len(segment) % 4)  # fix padding
        return json.loads(base64.urlsafe_b64decode(segment.encode()))

    header = b64_decode(header_b64)
    payload = b64_decode(payload_b64)

    return {
        "header": header,
        "payload": payload,
        "signature": signature
    }

def compare_dicts(d1, d2):
    all_keys = set(d1) | set(d2)
    for key in all_keys:
        v1 = d1.get(key)
        v2 = d2.get(key)
        if v1 != v2:
            print(f"[DIFF] {key}:")
            print(f"   Token 1 → {v1}")
            print(f"   Token 2 → {v2}")
        else:
            print(f"[SAME] {key}: {v1}")

def main():
    token1 = input("Token 1: ").strip()
    token2 = input("Token 2: ").strip()

    print("\n--- Comparing payloads ---\n")
    try:
        jwt1 = decode_jwt(token1)
        jwt2 = decode_jwt(token2)
    except Exception as e:
        print(f"Error decoding tokens: {e}")
        sys.exit(1)

    compare_dicts(jwt1["payload"], jwt2["payload"])

if __name__ == "__main__":
    main()
