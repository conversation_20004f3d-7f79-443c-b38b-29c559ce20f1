Voici la documentation de l’API **Simeo**, réécrite de façon claire et lisible :

---

## 🔗 **Base URL**

`https://phpstack-1478157-5631999.cloudwaysapps.com`

---

## 1. 🔐 Authentification & Utilisateur (`/api`)

| Méthode | URI                   | Auth | Description                             | Paramètres                                                             |
| ------- | --------------------- | ---- | --------------------------------------- | ---------------------------------------------------------------------- |
| POST    | `/api/login`          | ❌    | Connexion (JWT)                         | `email`, `password`                                                    |
| POST    | `/api/logout`         | ✅    | Déconnexion (invalide le token)         | –                                                                      |
| GET     | `/api/refresh`        | ✅    | Rafraîchissement du token               | –                                                                      |
| GET     | `/api/me`             | ✅    | Infos de l'utilisateur courant          | –                                                                      |
| POST    | `/api/signUp`         | ❌    | Création de compte                      | `name`, `email`, `password`, `password_confirmation`, `referral_code?` |
| POST    | `/api/confirmAccount` | ❌    | Confirmation par code reçu par courriel | `email`, `code`                                                        |
| POST    | `/api/changePassword` | ✅    | Changement de mot de passe              | `oldPassword`, `newPassword`, `newPasswordConfirmation`                |
| GET     | `/api/deleteAccount`  | ✅    | Suppression du compte                   | –                                                                      |
| POST    | `/api/forgotPassword` | ❌    | Envoi d’un code de réinitialisation     | `email`                                                                |
| POST    | `/api/resetPassword`  | ❌    | Réinitialisation du mot de passe        | `email`, `code`, `password`, `password_confirmation`                   |
| GET     | `/api/userInfo`       | ✅    | Alias de `/api/me`                      | –                                                                      |

---

## 2. 🌍 Données Publiques (`/api`)

| Méthode | URI                        | Auth | Description                       | Paramètres                 |
| ------- | -------------------------- | ---- | --------------------------------- | -------------------------- |
| GET     | `/api/bundles`             | ❌    | Forfaits disponibles pour un pays | `country`                  |
| GET     | `/api/bundlesUnlimited`    | ❌    | Forfaits illimités                | `country`                  |
| GET     | `/api/bundlesRegion`       | ❌    | Forfaits régionaux                | `country`                  |
| GET     | `/api/bundlesGo`           | ❌    | Forfaits optimisés « Simeo Go »   | `country`                  |
| GET     | `/api/countries`           | ❌    | Liste des pays + prix minimum     | –                          |
| GET     | `/api/getDiscounts`        | ❌    | Promotions actives                | –                          |
| GET     | `/api/getNetworkByCountry` | ❌    | Réseaux partenaires               | `country`, `distributorID` |
| GET     | `/api/getBalance`          | ❌    | Solde du distributeur             | `distributorID`            |

---

## 3. 🤝 Parrainage (`/api`)

| Méthode | URI                       | Auth | Description               | Paramètres |
| ------- | ------------------------- | ---- | ------------------------- | ---------- |
| GET     | `/api/referrals`          | ✅    | Liste des filleuls        | –          |
| POST    | `/api/changeReferralCode` | ✅    | Modifier son code parrain | –          |

---

## 4. 📱 Gestion eSIM (`/api/esim`)

| Méthode | URI                             | Auth | Description                   | Paramètres               |
| ------- | ------------------------------- | ---- | ----------------------------- | ------------------------ |
| GET     | `/api/esim/bundles`             | ✅    | Infos du bundle actif         | `iccID`, `distributorID` |
| GET     | `/api/esim/all`                 | ✅    | Toutes les eSIM et inventaire | –                        |
| GET     | `/api/esim/qrCode`              | ✅    | QR Code de l’eSIM             | `distributorID`          |
| GET     | `/api/esim/sendQrCode`          | ✅    | Envoi du QR Code par courriel | `distributorID`          |
| GET     | `/api/esim/sendConfirmationSMS` | ✅    | Envoi d’un SMS d’activation   | `distributorID`          |
| GET     | `/api/esim/compatibleDevices`   | ❌    | Appareils compatibles         | –                        |
| GET     | `/api/esim/getEsimInfoFromEsim` | ✅    | Détails de l’eSIM active      | `distributorID`          |
| POST    | `/api/changeActiveZone`         | ✅    | Changer la zone active        | `country`                |

---

## 5. 📦 Inventaire Bundles (`/api/bundles`)

| Méthode | URI                     | Auth | Description                    | Paramètres                       |
| ------- | ----------------------- | ---- | ------------------------------ | -------------------------------- |
| POST    | `/api/bundles/validate` | ✅    | Validation avant achat         | `iccid`, `name`, `distributorID` |
| POST    | `/api/bundles/apply`    | ✅    | Activation depuis l’inventaire | `name`, `distributorID`, `type?` |

---

## 6. 🧾 Commandes (`/api/orders`)

| Méthode | URI               | Auth | Description              |
| ------- | ----------------- | ---- | ------------------------ |
| GET     | `/api/orders/all` | ✅    | Historique des commandes |

---

## 7. 💳 Paiement Stripe (Webhooks)

| Méthode | URI                    | Auth    | Description              |
| ------- | ---------------------- | ------- | ------------------------ |
| POST    | `/stripePaymentIntent` | Externe | Webhook PaymentIntent    |
| POST    | `/stripeCheckout`      | Externe | Webhook Checkout         |
| POST    | `/checkoutCredit`      | Externe | Paiement de crédit Simeo |

---

## 8. 🧪 Routes Développement / Test

| Méthode | URI      | Description                               |
| ------- | -------- | ----------------------------------------- |
| GET     | `/begin` | Test de l'endpoint d’authentification     |
| GET     | `/list`  | Catalogue complet des bundles disponibles |

---

## 🔐 Authentification JWT

Pour les routes protégées, ajouter ce header :

```
Authorization: Bearer <token>
```

* Le token est obtenu via `/api/login`
* Valide pour **14 jours**
* Peut être renouvelé via `/api/refresh`

---

## ✅ Réponses & Erreurs

**Réponse standard :**

```json
{
  "status": "success",
  "msg": "..."
}
```

**Codes d’erreur :**

* `401 Unauthorized` : échec d’authentification
* `422 Unprocessable Entity` : erreur de validation des paramètres

---

## 🧰 Génération de documentation interactive

Utilisez des outils comme **Scribe** ou **L5-Swagger** pour générer une interface Swagger/OpenAPI, généralement disponible à l’URL :
`/docs`

---
