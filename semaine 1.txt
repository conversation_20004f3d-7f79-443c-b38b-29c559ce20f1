voici une **mise à jour structurée et claire** de mes actions et constats jusqu’à présent 

---

### 🔐 1. **Brute-force sur JWT**

* 📌 **Objectif** : tenter de découvrir la clé secrète utilisée pour signer les tokens JWT.
* 🔧 **Méthode** : script Python avec la wordlist `rockyou.txt` et `jwt.decode()` en boucle.
* 📚 **Librairie serveur détectée** : \[`tymon/jwt-auth`] (Laravel/Lumen).
* 🔎 **Résultat** : la clé secrète est suffisamment robuste, le brute-force est **inefficace**.
* ⚠️ **Note** : les tokens sont valides longtemps (TTL 14 jours), mais **aucune invalidation de l'ancien jeton** n’est faite après une opération comme `changeActiveZone`.

---

### 💥 2. **Analyse des endpoints pour vulnérabilité *mass assignment***

#### ✅ **Testés :**

| Endpoint                  | Méthode | Injection possible ?                              | Impact réel                                     | Statut        |
| ------------------------- | ------- | ------------------------------------------------- | ----------------------------------------------- | ------------- |
| `/api/me`                 | `GET`   | ❌ (lecture)                                       | ⚠️ Hash de mot de passe visible                  | ✅ Corrigé     |
| `/api/login`              | `POST`  | ❌ (pas de champ supplémentaire pertinent accepté) | ⚠️ Hash de mot de passe visible                  | ✅ Corrigé     |
| `/api/signUp`             | `POST`  | ✅ (ex : `credit`, `role`, `valid_account`)        | ❌ Pas d'effet observé                           | ✅ Corrigé     |
| `/api/changePassword`     | `POST`  | ✅ `role`, `credit`, etc.                          | ❌ Aucun effet malgré l'acceptation              | ✅ Corrigé     |
| `/api/changeActiveZone`   | `POST`  | ✅ `role`, `credit`, etc.                          | ❌ Champs ignorés mais acceptés                  | ❌ Non corrigé |



---

### ⚠️ 3. **Anomalies JWT / Sécurité**

* ❗**Invalide les jetons JWT ?** → **Non.**

  * Tu as démontré que le JWT reste fonctionnel **même après modification du compte** (`active_zone`).
  * Cela signifie **pas de révocation active ou blacklist de token**.

---

### 🔎 À faire ensuite ?

* Tester les autres endpoints critiques (avec ou sans effet) :
  `/api/logout`, `/api/esim/apply`, `/api/resetPassword` (en excluant `/deleteAccount`).
* Vérifier si une authentification côté admin (`/api/admin/users`) devient accessible après certaines manipulations.
* Inspecter la gestion des rôles sur la base de `/me` ou de tout retour de token (pas encore de preuve d'escalade).


