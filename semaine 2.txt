probleme de token qui reste actif:
création dun nouveau token chaque fois que je login. Utilisatation de ce token pour faire des requetes possible

mass assignement:
signUp: impossible
login: impossible
confirmAccount: impossible
changeActiveZone: possible
changePassword: impossible
changeReferralCode: possible
logout: possible
resetPassword: impossible
deleteAccount
bundles/validate: impossible
bundles/apply: impossible
forgotPassword: impossible
refresh (RAFRAÎCHIR LE TOKEN)
me (VÉRIFICATION DU PROFIL UTILISATEUR)




test sur le referal_code: essais dinjecter 2 code pour avoir plus de credit:
impossible


test sur les tokens la section prv: 23bd5c8949f600adb39e701c400872db7a5976f7 est pareil pour chaque utilisateur:
impossible descalader les droit car les droit sont gerer dans le cms via une base de donnée externe
"prv" semble etre decoratif ou par defaut

