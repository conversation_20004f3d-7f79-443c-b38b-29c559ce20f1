Ce que je test en ce moment :

Problème avec les tokens:
À chaque connexion, un *nouveau token JWT* est généré. Toutefois, *l'ancien token reste actif* et peut encore être utilisé pour faire des requêtes, ce qui représente une faille de sécurité (absence de révocation).


Ce qui est fait:

Tests de mass assignment:
Endpoints VULNÉRABLES au Mass Assignment (Status 200) :
POST /api/changeActiveZone
VULNÉRABLE - Accepte tous les paramètres supplémentaires
Réponse: {"status":"success"}
POST /api/changeReferralCode
VULNÉRABLE - Accepte tous les paramètres supplémentaires
Réponse: {"message":"success"}
POST /api/bundles/validate
VULNÉRABLE - Accepte tous les paramètres supplémentaires
Réponse: {"message":"Access denied"} (mais status 200)
POST /api/bundles/apply
VULNÉRABLE - Accepte tous les paramètres supplémentaires
Réponse: {"status":"error","message":"Vous n'avez pas de bundle test_bundle dans votre inventaire"}

Endpoints avec validation stricte (Status 422) :
POST /api/signUp
POST /api/login
POST /api/confirmAccount
POST /api/resetPassword
POST /api/forgotPassword
POST /api/changePassword


Test sur referral_code:
Tentative d’injection de *deux codes* pour obtenir plus de crédit : *inefficace*, le système ne prend en compte qu’un seul code.


Analyse du champ prv dans le token:
La valeur du champ "prv" (ex. : 23bd5c8949f600adb39e701c400872db7a5976f7) est *identique pour tous les utilisateurs*.
Ce champ ne permet pas d’escalader les droits, car ceux-ci sont *gérés dans le CMS via une base externe*.
Conclusion : "prv" semble être un champ *décoratif ou par défaut*, sans utilité réelle pour la sécurité.