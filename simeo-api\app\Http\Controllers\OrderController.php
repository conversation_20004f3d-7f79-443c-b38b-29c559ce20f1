<?php

namespace App\Http\Controllers;
use DB;
use Illuminate\Http\Request;
use App\Services\EsimGoService;
use <PERSON><PERSON>\Lumen\Routing\Controller as BaseController;

class OrderController extends BaseController
{
    
    public function __construct(EsimGoService $eSimGoService)
    {
        $this->eSimGoService = $eSimGoService;
        $this->middleware('auth:api', ['except' => []]);
    }

    public function getAllOrders(){
        
        $allOrders = array();
        $user_id = auth()->user()->id;
        $esims = DB::select("SELECT * FROM esims WHERE user_id = '" . $user_id . "'");
        foreach($esims as $esim){

            // get order reference
            $orders = DB::select("SELECT * FROM orders WHERE id_user = '" . $user_id . "'");

            $status = false;
            if(count($orders) > 0){
                $status = true;
            }

            foreach($orders as $key => $order){
                $payments = DB::select("SELECT * FROM paymentOrders WHERE id_order = '" . $order->id . "'");
                $priceBeforeTax = 0;
                if(count($payments) > 0){
                    $orders[$key]->payment = $payments[0];
                    $orders[$key]->payment->payment_object = json_decode($payments[0]->payment_object);
                    $orders[$key]->payment->fullPriceIncludeTax = $payments[0]->payment_object->amount / 100;
                    $orders[$key]->priceBeforeTax = number_format($orders[$key]->payment->fullPriceIncludeTax / 1.14975, 2, '.', '');
                }else{
                    $orders[$key]->priceBeforeTax = 0;
                }
                $product_name = $order->product_name;
                $product_name_separated = explode(" - ", $product_name);
                $order->country = $product_name_separated[1];
                $order->quantity = (int)filter_var($product_name_separated[2], FILTER_SANITIZE_NUMBER_INT);
                $order->fullDateTime = date('d-m-Y H:i:s', $order->date);
                $order->fullDate = date('d-m-Y', $order->date);
            }

            $allOrders[] = [
                'orders' => json_encode($orders),
                'status' => $status
            ];
        }

        return $allOrders;
    }

    public function addOrder($arrayOrder){
        $new_id = 0;
        //return $arrayOrder;

        $results = DB::insert("INSERT INTO orders (id_order, id_user, iccID, product_name, type, status, date, total_paid, payment_method, distributor_id, order_reference, credit) 
                                VALUES(  '" . $arrayOrder['id_order'] . "' ,
                                            '" . $arrayOrder['id_user'] . "',
                                            '" . $arrayOrder['iccID'] . "',
                                            '" . $arrayOrder['product_name'] . "',
                                            '" . $arrayOrder['type'] . "',
                                            '" . $arrayOrder['status'] . "',
                                            '" . $arrayOrder['date'] . "',
                                            '" . $arrayOrder['total_paid'] . "',
                                            '" . $arrayOrder['payment_method'] . "',
                                            '" . $arrayOrder['distributor_id'] . "',
                                            '" . $arrayOrder['order_reference'] . "',
                                            '" . $arrayOrder['credit'] . "' )");
        
        if($results){
            $new_id = DB::getPdo()->lastInsertId();
            $order = DB::select("UPDATE orders SET id_order = '" . $arrayOrder['id_order'] . "-" . $new_id . "' WHERE id = '" . $new_id . "'");
            return $new_id;
        }else{
            return false;
        }

        return $new_id;

    }

    public function addPaymentOrder($paymentIntent, $order_id){
            
            DB::transaction(function() use ($paymentIntent, $order_id) {
                $results = DB::insert("INSERT INTO paymentOrders (id_order, id_payment, id_customer, date, payment_object, status) 
                                        VALUES(  '" . $order_id . "' ,
                                                 '" . $paymentIntent->id . "',
                                                 '" . $paymentIntent->customer . "',
                                                 '" . $paymentIntent->created . "',
                                                 '" . json_encode($paymentIntent) . "',
                                                 '" . $paymentIntent->status . "')");

                
                if($results){
                    return true;
                }else{
                    return false;
                }
            });
    
    }


}