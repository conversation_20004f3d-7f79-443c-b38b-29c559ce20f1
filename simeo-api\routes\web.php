<?php

/** @var \Laravel\Lumen\Routing\Router $router */

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
|
| Here is where you can register all of the routes for an application.
| It is a breeze. Simply tell <PERSON><PERSON> the URIs it should respond to
| and give it the Closure to call when that URI is requested.
|
*/

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET,HEAD,PUT,POST,DELETE,PATCH,OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Auth-Token, Origin, Authorization');

$router->get('/', function ()  {
    return view('home_maintenance');
});

// DEV PURPOSE
$router->get('begin', 'AuthController@endpoint');
$router->get('list', 'ListController@catalogue');

#region GENERAL
Route::group([

    'prefix' => 'api'

], function () use ($router) {
    Route::post('login', 'AuthController@login');
    Route::post('logout', 'AuthController@logout');
    Route::get('refresh', 'AuthController@refresh');
    Route::get('me', 'AuthController@me');
    Route::get('bundles', 'PublicController@bundlesByCountry');
    Route::get('bundlesUnlimited', 'PublicController@bundlesByCountryUnlimited');
    Route::get('bundlesRegion', 'PublicController@bundlesByRegion');
    Route::get('bundlesGo', 'PublicController@bundlesByCountryGo');
    Route::get('countries', 'PublicController@countries');
    Route::get('getDiscounts', 'PublicController@getDiscounts');
    Route::post('confirmAccount', 'AuthController@confirmAccount');
    Route::get('userInfo', 'AuthController@me');
    Route::post('signUp', 'AuthController@signUp');
    Route::post('changePassword', 'AuthController@changePassword');
    Route::get('deleteAccount', 'AuthController@deleteAccount');
    Route::post('forgotPassword', 'AuthController@forgotPassword');
    Route::post('resetPassword', 'AuthController@resetPassword');
    Route::get('getNetworkByCountry', 'PublicController@getNetworkByCountry');
    Route::get('referrals', 'ReferralController@getReferrals');
    Route::post('changeReferralCode', 'ReferralController@changeReferralCode');
    Route::post('changeActiveZone', 'eSimController@changeActiveZone');
    Route::post('callbackEsimGo', 'CallBackController@callbackEsimGo');
});
#endregion

#region BUNDLES
Route::group([
    
    'prefix' => 'api/bundles'

], function () use ($router) {
    //Route::post('add', 'BundleController@add');
    Route::post('validate', 'BundleController@reloadValidateBeforeCheckout');
    Route::post('apply', 'BundleController@applyFromInventory');
    //Route::post('reload', 'BundleController@reloadEsim');
});
#endregion

#region ESIM
Route::group([
    
    'prefix' => 'api/esim'

], function () use ($router) {
    Route::get('bundles', 'eSimController@getBundlesInformations');
    Route::get('all', 'eSimController@getAllEsim');
    Route::get('qrCode', 'eSimController@getQrCode');
    Route::get('sendQrCode', 'eSimController@sendQrCode');
    //Route::get('sendQrCode2', 'eSimController@sendQrCode2');
    Route::get('sendConfirmationSMS', 'eSimController@sendConfirmationSMS');
    Route::get('compatibleDevices', 'eSimController@compatibleDevices');
    Route::get('getEsimInfoFromEsim', 'eSimController@getEsimInfoFromEsim');
    //Route::get('updateEsims', 'eSimController@updateEsims');
});
#endregion

#region ORDERS

Route::group([
    'prefix' => 'api/orders'
], function () use ($router) {
    Route::get('all', 'OrderController@getAllOrders');
});
#endregion

#region STRIPE

$router->post('stripePaymentIntent', 'StripeController@listenerPayment');
$router->post('stripeCheckout', 'StripeController@checkout');
$router->post('checkoutCredit', 'StripeController@checkoutCredit');

#endregion

