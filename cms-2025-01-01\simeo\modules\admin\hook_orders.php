<?php
require_once 'vendor/autoload.php';

function my_metabox() {
    if(isset($_GET['post'])){
        $post = get_post($_GET['post']);
        if ($post->post_type == 'orders') {
            add_meta_box( 'actions', 'Actions', 'action_button_meta_box', 'orders', 'side' );
        }
        if ($post->post_type == 'esims') {
            add_meta_box( 'esim', 'eSIM du client', 'esim_meta_box', 'esims', 'normal' );
        }
    }
}
add_action( 'admin_init', 'my_metabox' );

function action_button_meta_box() {

    global $wpdb;

    $post_id = $_GET['post'];
    $current_status = wp_get_object_terms($post_id, 'status');
    if($current_status[0]->slug == 'refund'){
        echo '<a href="#" class="button button-primary" disabled>Remboursé</a>';
    }else{
        echo '<h4>Remboursement</h4>';
        echo '<a href="/wp-admin/post.php?post=' . get_the_ID() . '&action=edit&refund=1" class="btn-red-simeo refund-stripe">Remboursement - Stripe</a><br/><br/>';
        echo '<a href="/wp-admin/post.php?post=' . get_the_ID() . '&action=edit&refundCredit=1" class="btn-red-simeo refund-credit">Remboursement - Crédit</a>';
    }

    $simeo_client_id = get_post_meta($post_id, 'id_user', true);
    $client_id = $wpdb->get_var("SELECT ID FROM wp_posts p INNER JOIN wp_postmeta pm ON p.ID = pm.post_id WHERE p.post_type = 'clients' AND pm.meta_key = 'client_id' AND pm.meta_value = '$simeo_client_id'");
    if($client_id != 0){
        echo '<h4>Navigation</h4>';
        echo '<a href="/wp-admin/post.php?post=' . $client_id . '&action=edit" class="btn-blue-simeo">Accéder à la fiche client</a><br/><br/>';
    }

    $forfait_achete = get_post_meta($post_id, 'produit_achete', true);
    $forfait_achete = explode('_', $forfait_achete);

    if(isset($forfait_achete[3])){
        $zone = get_region_from_iso($forfait_achete[3]);
        $esim = $wpdb->get_var("SELECT p.ID FROM wp_posts p INNER JOIN wp_postmeta pmZone ON p.ID = pmZone.post_id INNER JOIN wp_postmeta pmUser ON p.ID = pmUser.post_id WHERE p.post_type = 'esims' AND pmZone.meta_key = 'zone' AND pmZone.meta_value = '$zone' AND pmUser.meta_key = 'client_id' AND pmUser.meta_value = '$simeo_client_id'");
    }else{
        $zone = 'N/D';
        $esim = 0;
    }

    if($esim != 0){
        echo '<a href="/wp-admin/post.php?post=' . $esim . '&action=edit" class="btn-blue-simeo">Accéder à eSIM associée</a>';
    }

}

// REFUND STRIPE
add_action('admin_init', 'refund_order');
function refund_order() {
    $stripe_secret_key = '***********************************************************************************************************';
    if(isset($_GET['refund']) && $_GET['refund'] == 1) {

        $post_id = $_GET['post'];
        $order = get_post($post_id);
        $payment_id = get_post_meta($post_id, 'payment_id', true);
        
        // Refund Stripe
        try{
        $stripe = new \Stripe\StripeClient($stripe_secret_key);
            $stripe->refunds->create([
                'payment_intent' => $payment_id,
            ]);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            echo 'Caught exception: ',  $e->getMessage(), "\n";
        }

        wp_set_object_terms($post_id, 'rembourse', 'status');

        // update order status in datacenter
        $order_id = get_post_meta($post_id, 'numero_de_transaction', true);
        $conn = connect_to_datacenter();
        $query = "UPDATE orders SET status = 'Refunded' WHERE id_order = '$order_id'";
        $conn->query($query);
        $conn->close();

        // add message in top of the page
        add_action('admin_notices', 'refund_success_message');
        function refund_success_message() {
            echo '<div class="notice notice-success is-dismissible"><p>Commande remboursée avec succès via Stripe</p></div>';
        }
    }
}
// END STRIPE

// REFUND CREDIT
add_action('admin_init', 'refund_credit');
function refund_credit() {
    if(isset($_GET['refundCredit']) && $_GET['refundCredit'] == 1) {
        global $wpdb;
        //echo 'Refund credit';
        
        //if post is already refunded, do nothing
        $post_id = $_GET['post'];
        $current_status = wp_get_object_terms($post_id, 'status');
        if($current_status[0]->slug == 'refund'){
        
            add_action('admin_notices', 'refund_credit_already_message');
            function refund_credit_already_message() {
                echo '<div class="notice notice-success is-dismissible"><p>Commande déjà remboursée</p></div>';
            }
            return;
            
        }

        $conn = connect_to_datacenter();
        
        $post_id = $_GET['post'];
        $bundleToCredit = get_post_meta($post_id, 'produit_achete', true);

        // get updated bundles table
        $query = "SELECT meta_value FROM options WHERE meta_key = 'updated_bundles_table'";
        $result = $conn->query($query);
        $updateBundlesTable = $result->fetch_assoc()['meta_value'];
        
        // get real price (without taxes)
        $query = "SELECT * FROM " . $updateBundlesTable . " WHERE name = '$bundleToCredit'";
        $result = $conn->query($query);
        $bundleToCredit = $result->fetch_assoc();
        $real_price = $bundleToCredit['price'];
        
        // add credits to client (datacenter)
        $datacenter_client_id = get_post_meta($post_id, 'id_user', true);
        $query = "UPDATE users SET credit = credit + $real_price WHERE id = $datacenter_client_id";
        $conn->query($query);

        // add credits to client (cms)
        $cms_client_id = $wpdb->get_var("SELECT ID FROM wp_posts INNER JOIN wp_postmeta ON wp_posts.ID = wp_postmeta.post_id WHERE wp_postmeta.meta_key = 'client_id' AND wp_postmeta.meta_value = $datacenter_client_id");
        $current_credit = get_post_meta($cms_client_id, 'credits', true);
        if(empty($current_credit)){
            $current_credit = 0;
        }
        update_post_meta($cms_client_id, 'credits', floatval($current_credit) + floatval($real_price));

        // update order status in cms
        wp_set_object_terms($post_id, 'rembourse', 'status');

        // update order status in datacenter
        $order_id = get_post_meta($post_id, 'numero_de_transaction', true);
        $query = "UPDATE orders SET status = 'Refunded' WHERE id_order = '$order_id'";
        $conn->query($query);
        $conn->close();

        // add message in top of the page
        add_action('admin_notices', 'refund_credit_success_message');
        function refund_credit_success_message() {
            echo '<div class="notice notice-success is-dismissible"><p>Commande remboursée avec succès via Crédit</p></div>';
        }
    }
}

?>