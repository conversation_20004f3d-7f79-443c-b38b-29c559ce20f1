{"name": "laravel/lumen", "description": "The Laravel Lumen Framework.", "keywords": ["framework", "laravel", "lumen"], "license": "MIT", "type": "project", "require": {"php": "^8.1", "flipbox/lumen-generator": "^9.2", "laravel/lumen-framework": "^10.0", "stripe/stripe-php": "^10.13", "tymon/jwt-auth": "*"}, "require-dev": {"fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.4.4", "phpunit/phpunit": "^10.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}