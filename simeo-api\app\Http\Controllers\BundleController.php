<?php

namespace App\Http\Controllers;
use DB;
use Illuminate\Http\Request;
use App\Services\EsimGoService;
use App\Http\Controllers\eSimController;
use App\Http\Controllers\OrderController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\Lumen\Routing\Controller as BaseController;

class BundleController extends BaseController
{
    
    public $iccID;
    public $bundleName;
    public $startTime;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(EsimGoService $eSimGoService, eSimController $eSimController, OrderController $orderController)
    {
        $this->eSimGoService = $eSimGoService;
        $this->eSimController = $eSimController;
        $this->orderController = $orderController;
        $this->middleware('auth:api', ['except' => []]);
    }

    public function addFirstBundle($name, $distributorId, $paymentIntent, $userID, $credit = 0, $paymentMethod = "Stripe"){

        $this->bundleName = $name;
        $this->distributorID = $distributorId;
        $this->credit = $credit;
        $this->paymentMethod = $paymentMethod;

        // E-SIMGO
        if($this->distributorID == 1){
            $order = json_decode($this->eSimGoService->add($this->bundleName));
            $apply = $this->eSimGoService->apply($this->bundleName, "");
            $apply = json_decode($apply);


            //$eSIM_number = $apply->order[0]->esims[0]->iccid;

            Log::info(print_r($apply, true));
            $eSIM_number = $apply->esims[0]->iccid;
            $apply_reference = $apply->applyReference;

            $bundleInfo = getBundleName($this->bundleName, $this->distributorID);
            $bundleInfo->price = $this->getBundlePrice($this->bundleName);

            // Get user credit by sql
            $user_credit = DB::select("SELECT credit FROM users WHERE id = '" . $userID . "'");
            $user_credit = $user_credit[0]->credit;

            if($bundleInfo->dataAmount == -1){
                $dataAmount = "Unlimited";
            }else{
                $dataAmount = floatval($bundleInfo->dataAmount) / 1000 . "GB";
            }

            $bundleName = 'eSIM - ' . $bundleInfo->main_country . " - " . $dataAmount . " - " . $bundleInfo->duration . " Jours";    

            // Update credit user
            if($this->credit > 0){
                $user_credit = $user_credit - $this->credit;
                if($this->credit == $bundleInfo->price){
                    $this->paymentMethod = "Crédit Simeo";
                }else{
                    $this->paymentMethod = "Stripe / Crédit Simeo";
                }
                
                $user = DB::select("UPDATE users SET credit = '" . $user_credit . "' WHERE id = '" . $userID . "'");
            }


            $total_paid = (float)$bundleInfo->price - $this->credit;
            if($total_paid < 0){
                $total_paid = 0;
            }

            $arrayOrder = array(
                'id_order' => date("Ymd"),
                'id_user' => $userID,
                'iccID' => $eSIM_number,
                'product_name' => $bundleName,
                'type' => 'eSIM',
                'status' => 'Completed',
                'date' => time(),
                'total_paid' => (float)$total_paid,
                'payment_method' => $this->paymentMethod,
                'distributor_id' => $this->distributorID,
                'order_reference' => $order->orderReference,
                'credit' => $credit
            );

            // get country code
            $countryCode = $bundleInfo->main_country;

            $order_id = $this->orderController->addOrder($arrayOrder);
            $this->eSimController->changeActiveZoneFromBundle($countryCode);
            $this->eSimController->addEsimToUser($userID, $eSIM_number, $order_id, $apply_reference, $countryCode);
            
            if($order_id != false && $paymentMethod != "Crédit Simeo"){
                if(!empty($paymentIntent)){
                    $payment_id = $this->orderController->addPaymentOrder($paymentIntent, $order_id);
                    // Enregistrement du stripe_customer_id pour gestion des cartes de crédit
                    $customer_id = $paymentIntent->customer;
                    $customer = DB::select("UPDATE users SET stripe_customer_id = '" . $customer_id . "' WHERE id = '" . $userID . "'");
                }
            }

        }

        return 1;

    }



    public function applyFromInventory(Request $request){
        $this->validate($request, [
            'name' => 'required|string',
            'distributorID' => 'required|integer',
            'type' => 'string|nullable'
        ]);

        $infoBundle = explode("_", $request->input('name'));
        if(isset($infoBundle[3])){
            $mainCountry = $infoBundle[3];
            $mainCountry = $this->eSimController->validateZone($mainCountry);
        }else{
            $mainCountry = "";
        }

        $esim = "";
        $user = auth()->user();
        if($request->input('type') == "changeEsim"){
            $esim = "";
        }else{
            $esim = DB::select("SELECT * FROM esims WHERE zone = '" . $mainCountry . "' AND user_id = '" . $user->id . "'" );
            if(count($esim) == 0){
                $esim = "";
            }else{
                $esim = $esim[0]->iccid;
            }
        }

        $hasBundle = DB::select("SELECT * FROM inventories WHERE user_id = '" . $user->id . "' AND bundleName = '" . $request->input('name') . "'");
        if(count($hasBundle) == 0){
            return [
                'status' => 'error',
                'message' => 'Vous n\'avez pas de bundle ' . $request->input('name') . ' dans votre inventaire'
            ];
        }

        $this->bundleName = $request->input('name');
        $this->distributorID = $request->input('distributorID');

        // E-SIMGO
        if($this->distributorID == 1){
            $order = $this->eSimGoService->apply($this->bundleName, $esim);
            $order = json_decode($order);
            if(isset($order->message) && $order->message == "Incompatible bundle profile combination"){
                // Si le bundle n'est pas compatible avec l'eSIM
                return [
                    'status' => 'error',
                    'message' => $order->message
                ];
            }else{
                // Si on ne trouve pas d'eSIM qui concorde avec la zone ( pays ) du bundle
                if($esim == "" && isset($order->esims[0]->iccid)){
                    $esim = $order->esims[0]->iccid;
                    $apply_reference = $order->applyReference;
                    $deleteFromInventory = DB::select("DELETE FROM inventories WHERE user_id = '" . $user->id . "' AND bundleName = '" . $this->bundleName . "' LIMIT 1");
                    //$user = DB::select("UPDATE esims SET iccid = '" . $esim . "', apply_reference = '" . $apply_reference . "', created_at = '" . time() . "', image_url = '' WHERE user_id = '" . $user->id . "'");
                    $this->eSimController->addEsimToUser($user->id, $esim, "", $apply_reference, $mainCountry);
                    DB::select('INSERT INTO apply_history (esim, bundle, user_id, dateApply) VALUES (?, ?, ?, ?)', [$esim, $this->bundleName, $user->id, date('Y-m-d H:i:s')]);
                    return [
                        'status' => 'success',
                        'message' => json_encode($order),
                        'esim' => $esim
                    ]; 
                }else{
                    if(isset($order->esims[0]->iccid)){
                        $esim = $order->esims[0]->iccid;
                        $apply_reference = $order->applyReference;
                        // update eSIM
                        DB::select("UPDATE esims SET last_apply_date = '" . date('Ymd') . "', apply_reference = '" . $apply_reference . "'WHERE user_id = '" . $user->id . "' and zone = '" . $mainCountry . "'");
                        $deleteFromInventory = DB::select("DELETE FROM inventories WHERE user_id = '" . $user->id . "' AND bundleName = '" . $this->bundleName . "' LIMIT 1");
                        DB::select('INSERT INTO apply_history (esim, bundle, user_id, dateApply) VALUES (?, ?, ?, ?)', [$esim, $this->bundleName, $user->id, date('Y-m-d H:i:s')]);
                        return [
                            'status' => 'success',
                            'message' => json_encode($order),
                            'esim' => $esim
                        ];
                    }else{
                        return [
                            'status' => 'error',
                            'message' => json_encode($order)
                        ];
                    }
                }
                
            }
        }
    }


    #region reloadEsim ( DEPRECATED )
    public function reloadValidateBeforeCheckout(Request $request){
        $this->validate($request, [
            'iccid' => 'required|string',
            'name' => 'required|string',
            'distributorID' => 'required|integer'
        ]);

        $this->iccID = $request->input('iccid');
        $this->bundleName = $request->input('name');
        $this->distributorID = $request->input('distributorID');
        $order = "error";
        if($this->distributorID == 1){
            $order = json_decode($this->eSimGoService->reloadValidateBeforeCheckout($this->bundleName, $this->iccID));
        }

        return json_encode($order);
    }

    public function addToInventory($iccID, $bundleName, $distributorID, $paymentIntent, $userID, $credit = 0, $paymentMethod = "Stripe"){
        /*$this->validate($request, [
            'iccid' => 'required|string',
            'name' => 'required|string',
            'distributorID' => 'required|integer'
        ]);*/

        // Get user credit by sql
        $user_credit = DB::select("SELECT credit FROM users WHERE id = '" . $userID . "'");
        $user_credit = $user_credit[0]->credit;

        $this->iccID = $iccID;
        $this->bundleName = $bundleName;
        $this->distributorID = $distributorID;
        $this->credit = $credit;
        $this->paymentMethod = $paymentMethod;

        if($this->distributorID == 1){

            // Appel vers l'API eSimGo
            $order = json_decode($this->eSimGoService->addToInventory($this->bundleName, $this->iccID));
            //Log::info(print_r($order, true));

            // Get bundle info
            $bundleInfo = getBundleName($this->bundleName, $this->distributorID);
            $bundleInfo->price = $this->getBundlePrice($this->bundleName);

            if($bundleInfo->dataAmount == -1){
                $dataAmount = "Unlimited";
            }else{
                $dataAmount = floatval($bundleInfo->dataAmount) / 1000 . "GB";
            }

            $bundleName = 'eSIM - ' . $bundleInfo->main_country . " - " . $dataAmount . " - " . $bundleInfo->duration . " Jours";   

            // Update credit user
            Log::info("user_credit : " . $user_credit);
            Log::info("credit : " . $this->credit);
            if($this->credit > 0){
                
                $user_credit = $user_credit - $this->credit;
                if($this->credit == $bundleInfo->price){
                    $this->paymentMethod = "Crédit Simeo";
                }else{
                    $this->paymentMethod = "Stripe / Crédit Simeo";
                }
                
                $user = DB::select("UPDATE users SET credit = '" . $user_credit . "' WHERE id = '" . $userID . "'");
            }

            $total_paid = (float)$bundleInfo->price - $this->credit;
            if($total_paid < 0){
                $total_paid = 0;
            }

            $arrayOrder = array(
                'id_order' => date("Ymd"),
                'id_user' => $userID,
                'iccID' => $this->iccID,
                'product_name' => $bundleName,
                'type' => 'reload',
                'status' => 'Completed',
                'date' => time(),
                'total_paid' => (float)$total_paid,
                'payment_method' => $this->paymentMethod,
                'distributor_id' => $this->distributorID,
                'order_reference' => $order->orderReference,
                'credit' => $this->credit
            );

            // TODO - Finir reload ( voir addFirstBundle )

            $order_id = $this->orderController->addOrder($arrayOrder);
            $inventory = DB::insert("INSERT INTO inventories (user_id, bundleName) VALUES ('" . $userID . "', '" . $this->bundleName . "')");

            if($order_id != false && $paymentMethod != "Crédit Simeo"){
                if(!empty($paymentIntent)){
                    $payment_id = $this->orderController->addPaymentOrder($paymentIntent, $order_id);

                    // Enregistrement du stripe_customer_id pour gestion des cartes de crédit
                    $customer_id = $paymentIntent->customer;
                    $customer = DB::select("UPDATE users SET stripe_customer_id = '" . $customer_id . "' WHERE id = '" . $userID . "'");
                }
            }

            // AJOUT COMMANDE BD
            //$applyResult = $this->eSimGoService->apply($this->bundleName, $this->iccID);
        }

        return [];
    }
    #endregion

    public function getBundlePrice($name){
        $bundleTable = getUpdatedBundleTable();
        $bundle = DB::select("SELECT * FROM " . $bundleTable . " WHERE name = '" . $name . "'");
        $bundle = $bundle[0];

        // Vérifier si un rabais est actif pour ce bundle
        $discount = DB::select("SELECT * FROM discounts WHERE bundleName = '" . $name . "'");
        if(count($discount) > 0){
            if($discount[0]->is_active == 1){
                $discount = $discount[0];
                $bundle->price = $discount->price;
                $bundle->is_discount = true;
                $bundle->discount = $discount;
            }else{
                $bundle->price = $discount[0]->regular_price;
                $bundle->is_discount = false;
                $bundle->discount = $discount[0];
            }
        }

        if(isset(auth()->user()->credit)){
            $credit = auth()->user()->credit;
            if(is_numeric($credit) && $credit > 0){
                $bundle->price = floatval($bundle->price) - floatval($credit);
                if($bundle->price < 0){
                    $bundle->price = 0;
                }
            }
        }
        $price = floatval($bundle->price) + floatval($bundle->price) * 0.05 + floatval($bundle->price) * 0.09975;
        $price = round($price, 2);

        return $price;
    }

    public function getBundleMinPrice($iso){
        $bundleTable = getUpdatedBundleTable();
        $bundle = DB::select("SELECT * FROM " . $bundleTable . " WHERE main_country = '" . $iso . "' ORDER BY price ASC LIMIT 1");
        $bundleInfo = explode("_", $bundle[0]->name);
        $country = $bundleInfo[3];
        $countDiscount = DB::select("SELECT * FROM discounts WHERE bundleName LIKE '%_" . $iso . "_%'");
        if(count($bundle) > 0){
            // check discount
            $discount = DB::select("SELECT * FROM discounts WHERE bundleName = '" . $bundle[0]->name . "'");
            
            $is_discount = false;
            if(count($countDiscount) > 0){
                $is_discount = true;
            }
            if(count($discount) > 0){
                if($discount[0]->is_active == 1){
                    $bundle[0]->price = $discount[0]->price;
                }else{
                    $bundle[0]->price = $discount[0]->regular_price;
                }
            }

            return [$bundle[0]->price, $is_discount];
        }else{
            return [0, false];
        }
    }

    public function getBundlePriceWithDiscount($bundle){
        $discount = DB::select("SELECT * FROM discounts WHERE bundleName = '" . $bundle->name . "' AND percentage > 9");
        if(count($discount) > 0){
            if($discount[0]->is_active == 1){
                $bundle->old_price = $discount[0]->regular_price;
                $bundle->price = $discount[0]->price;
                $bundle->is_discount = true;
                $bundle->discount = $discount[0];
            }else{
                $bundle->price = $discount[0]->regular_price;
                $bundle->is_discount = false;
                $bundle->discount = $discount[0];
            }

        }else{
            $bundle->is_discount = false;
        }
        return $bundle;
    }

    public function getBundleCreditUsed($name, $credit){
        $bundleTable = getUpdatedBundleTable();
        $bundle = DB::select("SELECT * FROM " . $bundleTable . " WHERE name = '" . $name . "'");
        if(count($bundle) == 0 || !isset($bundle[0])){
            return 0;
        }

        $bundle = $bundle[0];

        // Vérifier si un rabais est actif pour ce bundle
        $discount = DB::select("SELECT * FROM discounts WHERE bundleName = '" . $name . "'");
        if(count($discount) > 0){
            if($discount[0]->is_active == 1){
                $bundle->price = $discount[0]->price;
            }else{
                $bundle->price = $discount[0]->regular_price;
            }
        }
        
        if(is_numeric($credit) && $credit > 0){
            $old_price = $bundle->price;
            if(floatval($credit) > floatval($bundle->price)){
                $credit_used = floatval($credit) - floatval($bundle->price);
                $credit_used = floatval($credit) - $credit_used;
            }else{
                $bundle->price = floatval($bundle->price) - floatval($credit);
                $credit_used = $old_price - $bundle->price;
            }
            
        }else{
            $credit_used = 0;
        }
        return $credit_used;
    }

}
