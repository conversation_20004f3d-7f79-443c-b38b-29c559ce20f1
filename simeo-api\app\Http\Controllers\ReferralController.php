<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReferralController extends Controller
{
    public function getReferrals(Request $request)
    {   
        $user_id = auth()->user()->id;
        $referrals = DB::table('referrals')
            ->join('users', 'referrals.new_user', '=', 'users.id')
            ->where('referrals.referred_by', $user_id)
            ->select('referrals.*', 'users.email as new_user_email')
            ->get();

        return response()->json($referrals);
    }

    public function changeReferralCode(Request $request)
    {
        $user_id = auth()->user()->id;
        $referral_code = $request->referralCode;

        // Get the old referral code
        $old_referral_code = DB::table('users')
            ->where('id', $user_id)
            ->value('my_referral_code');

        if($old_referral_code == $referral_code) {
            return response()->json(['message' => 'success']);
        }

        if(empty($referral_code) || strlen($referral_code) < 6 || strlen($referral_code) > 20) {
            return response()->json(['message' => 'invalid_referral_code'], 400);
        }

        if (preg_match('/^SIMEO\d+$/', $referral_code)) {
            return response()->json(['message' => 'reserved_referral_code'], 400);
        }

        // Validate that the referral code is not already taken
        $existing_referral = DB::table('users')
            ->where('my_referral_code', $referral_code)
            ->exists();

        if ($existing_referral) {
            return response()->json(['message' => 'referral_code_already_taken'], 400);
        }

        // Update the user's referral code
        DB::table('users')
            ->where('id', $user_id)
            ->update(['my_referral_code' => $referral_code]);

        // Update the referrals with the old referral code
        DB::table('users')
            ->where('referral_by', $old_referral_code)
            ->update(['referral_by' => $referral_code]);

        return response()->json(['message' => 'success']);
    }
}
