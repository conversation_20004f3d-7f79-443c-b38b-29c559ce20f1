<?php

function create_post_type_orders() {
    register_post_type('orders',
        array(
            'labels'      => array(
                'name'          => __('Commandes'),
                'singular_name' => __('Commande')
            ),
            'public'      => true,
            'has_archive' => false,
            'rewrite'     => array('slug' => 'orders'),
            'show_in_rest' => false, // Active<PERSON>
            'supports'    => array('title')
        )
    );

    register_taxonomy('status', 'orders', array(
        'labels' => array(
            'name' => __('Status'),
            'singular_name' => __('Status')
        ),
        'hierarchical' => true,
        'public' => false,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'status'),
    ));

}
add_action('init', 'create_post_type_orders');

