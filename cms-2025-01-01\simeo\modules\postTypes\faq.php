<?php

function create_post_type_faq() {
    register_post_type('faq',
        array(
            'labels'      => array(
                'name'          => __('FAQs'),
                'singular_name' => __('FAQ')
            ),
            'public'      => true,
            'has_archive' => true,
            'rewrite'     => array('slug' => 'faqs'),
            'show_in_rest' => true, // <PERSON><PERSON> G<PERSON>
            'supports'    => array('title', 'editor', 'author', 'thumbnail', 'excerpt', 'comments')
        )
    );

    register_taxonomy('language', 'faq', array(
        'labels' => array(
            'name' => __('Languages'),
            'singular_name' => __('Language')
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'languages'),
    ));

    register_taxonomy('category', 'faq', array(
        'labels' => array(
            'name' => __('Categories'),
            'singular_name' => __('Category')
        ),
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'categories'),
    ));
}
add_action('init', 'create_post_type_faq');

