📊 Analyse des résultats :

✅ Endpoints VULNÉRABLES au Mass Assignment (Status 200) :
POST /api/changeActiveZone
✅ VULNÉRABLE - Accepte tous les paramètres supplémentaires
Réponse: {"status":"success"}
POST /api/changeReferralCode
✅ VULNÉRABLE - Accepte tous les paramètres supplémentaires
Réponse: {"message":"success"}
POST /api/bundles/validate
✅ VULNÉRABLE - Accepte tous les paramètres supplémentaires
Réponse: {"message":"Access denied"} (mais status 200)
POST /api/bundles/apply
✅ VULNÉRABLE - Accepte tous les paramètres supplémentaires
Réponse: {"status":"error","message":"Vous n'avez pas de bundle test_bundle dans votre inventaire"}

❌ Endpoints avec validation stricte (Status 422) :
POST /api/signUp
POST /api/login
POST /api/confirmAccount
POST /api/resetPassword
POST /api/forgotPassword
POST /api/changePassword

🚨 Vulnérabilités confirmées :
4 endpoints acceptent des paramètres de mass assignment incluant :

credit: 999999
role: "admin"
valid_account: 1
stripe_customer_id: "cus_admin"
Et tous les autres paramètres malveillants